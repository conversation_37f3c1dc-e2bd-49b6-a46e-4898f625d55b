import 'package:flutter/material.dart';
import 'package:instsaver/Modal/AccountDataModal.dart';
import 'package:instsaver/Modal/SearchModal.dart';
import 'package:instsaver/Services/InstaUserServices.dart';

import '../../Utility/Constant.dart';
import 'UserProfileScreen.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  TextEditingController usernameController = TextEditingController();
  SearchPost? searchPost;
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.appwhite,
      appBar: AppBar(
        centerTitle: true,
        title: Text("Search"),
        //backgroundColor: Colors.transparent,
        foregroundColor: AppColor.appwhite,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: TextForm<PERSON><PERSON>(
                    controller: usernameController,
                    cursorColor: Colors.black,
                    decoration: InputDecoration(
                      filled: true,
                      fillColor: AppColor.accent1,
                      hintText: 'Search Username',
                      hintStyle: AppTextStyle.hometextfieldhintStyle,
                      suffixIcon: usernameController.text.isEmpty
                          ? null
                          : IconButton(
                              onPressed: () {
                                usernameController.clear();
                              },
                              icon: const Icon(Icons.cancel_sharp),
                              color: Colors.grey,
                            ),
                      contentPadding: const EdgeInsets.only(
                          left: 20, right: 20, top: 16, bottom: 16),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(50.0),
                        borderSide:
                            const BorderSide(color: Colors.grey, width: 2),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(50.0),
                        borderSide:
                            const BorderSide(color: Colors.grey, width: 2),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(50.0),
                        borderSide:
                            const BorderSide(color: Colors.grey, width: 2),
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {});
                    },
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (usernameController.text.trim().isEmpty == true) {
                      SnackBar(
                        content: Text('Username is required.'),
                      );
                    } else {
                      isLoading = true;
                      setState(() {});
                      searchPost = await InstaUserServices.instaUserServices
                              .search(query: usernameController.text) ??
                          SearchPost();
                      isLoading = false;
                      setState(() {});
                    }
                  },
                  child: isLoading
                      ? SizedBox(
                          height: 24,
                          width: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Icon(Icons.search),
                  style: ElevatedButton.styleFrom(
                    alignment: Alignment.center,
                    backgroundColor: AppColor.primaryColor,
                    foregroundColor: AppColor.foregroundbtnColor,
                    minimumSize: Size(50, 50),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10,
          ),
          Expanded(
            child: searchPost == null
                ? Center(
                    child: Text(
                      'Search User',
                      style: AppTextStyle.captionStyle
                          .copyWith(color: AppColor.primaryColor),
                    ),
                  )
                : ListView.separated(
                    separatorBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: const Divider(
                          height: 0.5,
                        ),
                      );
                    },
                    itemCount: searchPost!.data!.items!.length,
                    itemBuilder: (context, index) {
                      return ListTile(
                        onTap: () async {
                          AccountData? accountData = await InstaUserServices
                              .instaUserServices
                              .fetchUserData(
                                  searchPost!.data!.items![index].username!);

                          if (accountData != null) {
                            Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) =>
                                  UserProfileScreen(accountData: accountData),
                            ));
                          }
                        },
                        leading: ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: Image.network(
                              searchPost!.data!.items![index].profilePicUrl!),
                        ),
                        title: Text(
                          searchPost!.data!.items![index].fullName!,
                        ),
                        subtitle: Text(
                          searchPost!.data!.items![index].username!,
                        ),
                        trailing: IconButton(
                          onPressed: () {},
                          icon: const Icon(
                            Icons.chevron_right,
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
