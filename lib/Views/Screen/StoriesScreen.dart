import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../Modal/AccountDataModal.dart';
import '../../Modal/StoryPostModal.dart';
import '../../Services/InstaUserServices.dart';
import '../../Utility/Constant.dart';
import 'UserStoriesScreen.dart';

class StoriesScreen extends StatefulWidget {
  final AccountData user;
  const StoriesScreen({super.key, required this.user});

  @override
  State<StoriesScreen> createState() => _StoriesScreenState();
}

class _StoriesScreenState extends State<StoriesScreen> {
  List<StoryDataItem> userStories = [];
  bool hasStories = true;
  @override
  void initState() {
    super.initState();
    fetchUserStories();
  }

  Future<void> fetchUserStories() async {
    StoryPost Story = await InstaUserServices.instaUserServices.fetchStories(username: widget.user.data?.username);
    userStories.addAll(Story.data?.items ?? []);
    setState(() {
      userStories.isEmpty ? hasStories = false : hasStories = true;
    });
    Logger().d(hasStories);
    print(userStories.length);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios_new_rounded,
              color: AppColor.appwhite,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          title: Text(
            'Stories',
            style: AppTextStyle.appbarTextStyle.copyWith(fontSize: 18),
          ),
        ),
        body: userStories.isNotEmpty
            ? GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  childAspectRatio: 1 / 1,
                  crossAxisCount: 3,
                  crossAxisSpacing: 2,
                  mainAxisSpacing: 2,
                ),
                itemCount: userStories.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {},
                    child: GestureDetector(
                      onTap: () {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => UserStoriesScreen(
                                  user: widget.user,
                                  storyPost: userStories[index],
                                )));
                      },
                      child: Container(
                        alignment: Alignment.topLeft,
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.transparent,
                            ),
                            userStories[index].mediaType == 2
                                ? Icon(
                                    Icons.play_circle_outline,
                                    color: Colors.white,
                                  )
                                : Container(),
                          ],
                        ),
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: NetworkImage(
                                  userStories[index]
                                          .imageVersions
                                          ?.items?[0]
                                          .url ??
                                      '',
                                ),
                                fit: BoxFit.cover)),
                      ),
                    ),
                  );
                })
            : hasStories == false
                ? Center(
                    child: Text(
                    'No Stories',
                    style: AppTextStyle.captionTitleStyle,
                  ))
                : Center(
                    child: CircularProgressIndicator(),
                  ));
  }
}
