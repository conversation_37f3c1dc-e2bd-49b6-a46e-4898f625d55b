import 'package:flutter/material.dart';

/// LUMINA Design System - Revolutionary Gallery App Theme
/// Combines minimalism with futuristic aesthetics
class LuminaTheme {
  // === CORE BRAND COLORS ===
  static const Color primaryNeon = Color(0xFF00F5FF); // Electric Cyan
  static const Color secondaryGlow = Color(0xFFFF006E); // Magenta Pulse
  static const Color accentAura = Color(0xFF8B5CF6); // Purple Aura
  static const Color warningFlare = Color(0xFFFFBE0B); // Solar Flare
  
  // === NEURAL DARK THEME ===
  static const Color darkVoid = Color(0xFF0A0A0F); // Deep Space
  static const Color darkSurface = Color(0xFF1A1A2E); // Cosmic Surface
  static const Color darkElevated = Color(0xFF16213E); // Elevated Panel
  static const Color darkText = Color(0xFFE8E8F0); // Neural White
  static const Color darkSubtext = Color(0xFFB8B8C8); // Soft Gray
  
  // === LIGHT QUANTUM THEME ===
  static const Color lightQuantum = Color(0xFFFAFAFC); // Quantum White
  static const Color lightSurface = Color(0xFFF0F0F5); // Light Surface
  static const Color lightElevated = Color(0xFFFFFFFF); // Pure White
  static const Color lightText = Color(0xFF1A1A2E); // Dark Text
  static const Color lightSubtext = Color(0xFF6B7280); // Medium Gray
  
  // === GRADIENT COLLECTIONS ===
  static const LinearGradient neonFlow = LinearGradient(
    colors: [primaryNeon, secondaryGlow],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient auraGlow = LinearGradient(
    colors: [accentAura, primaryNeon],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  static const LinearGradient cosmicDust = LinearGradient(
    colors: [darkVoid, darkSurface, darkElevated],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // === TYPOGRAPHY SYSTEM ===
  static const String primaryFont = 'SF Pro Display';
  static const String secondaryFont = 'Inter';
  
  static const TextStyle displayLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 32,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.5,
    height: 1.2,
  );
  
  static const TextStyle displayMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: 24,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.3,
    height: 1.3,
  );
  
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: secondaryFont,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
    height: 1.5,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: secondaryFont,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.1,
    height: 1.4,
  );
  
  static const TextStyle labelSmall = TextStyle(
    fontFamily: secondaryFont,
    fontSize: 12,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    height: 1.3,
  );
  
  // === SPACING SYSTEM ===
  static const double spaceXS = 4.0;
  static const double spaceSM = 8.0;
  static const double spaceMD = 16.0;
  static const double spaceLG = 24.0;
  static const double spaceXL = 32.0;
  static const double space2XL = 48.0;
  static const double space3XL = 64.0;
  
  // === BORDER RADIUS ===
  static const double radiusXS = 4.0;
  static const double radiusSM = 8.0;
  static const double radiusMD = 12.0;
  static const double radiusLG = 16.0;
  static const double radiusXL = 24.0;
  static const double radiusFull = 999.0;
  
  // === SHADOWS & ELEVATION ===
  static const BoxShadow neuralShadow = BoxShadow(
    color: Color(0x1A000000),
    blurRadius: 20,
    offset: Offset(0, 8),
    spreadRadius: 0,
  );
  
  static const BoxShadow glowShadow = BoxShadow(
    color: Color(0x3300F5FF),
    blurRadius: 30,
    offset: Offset(0, 0),
    spreadRadius: 5,
  );
  
  // === ANIMATION CURVES ===
  static const Curve fluidCurve = Curves.easeOutCubic;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve snapCurve = Curves.easeInOutBack;
  
  // === THEME DATA ===
  static ThemeData get darkTheme => ThemeData(
    brightness: Brightness.dark,
    primaryColor: primaryNeon,
    scaffoldBackgroundColor: darkVoid,
    cardColor: darkSurface,
    dividerColor: darkElevated,
    textTheme: const TextTheme(
      displayLarge: TextStyle(color: darkText),
      displayMedium: TextStyle(color: darkText),
      bodyLarge: TextStyle(color: darkText),
      bodyMedium: TextStyle(color: darkSubtext),
      labelSmall: TextStyle(color: darkSubtext),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      iconTheme: IconThemeData(color: darkText),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarTheme(
      backgroundColor: darkSurface,
      selectedItemColor: primaryNeon,
      unselectedItemColor: darkSubtext,
    ),
  );
  
  static ThemeData get lightTheme => ThemeData(
    brightness: Brightness.light,
    primaryColor: accentAura,
    scaffoldBackgroundColor: lightQuantum,
    cardColor: lightSurface,
    dividerColor: lightElevated,
    textTheme: const TextTheme(
      displayLarge: TextStyle(color: lightText),
      displayMedium: TextStyle(color: lightText),
      bodyLarge: TextStyle(color: lightText),
      bodyMedium: TextStyle(color: lightSubtext),
      labelSmall: TextStyle(color: lightSubtext),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      iconTheme: IconThemeData(color: lightText),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarTheme(
      backgroundColor: lightElevated,
      selectedItemColor: accentAura,
      unselectedItemColor: lightSubtext,
    ),
  );
}

/// Custom Icons for Lumina
class LuminaIcons {
  static const IconData neural = Icons.auto_awesome;
  static const IconData memory = Icons.psychology;
  static const IconData cluster = Icons.hub;
  static const IconData flow = Icons.waves;
  static const IconData aura = Icons.blur_circular;
  static const IconData quantum = Icons.scatter_plot;
  static const IconData pulse = Icons.favorite;
  static const IconData void_space = Icons.dark_mode;
  static const IconData light_beam = Icons.light_mode;
  static const IconData search_ai = Icons.search;
  static const IconData face_detect = Icons.face;
  static const IconData object_scan = Icons.center_focus_strong;
  static const IconData time_flow = Icons.timeline;
  static const IconData emotion_map = Icons.sentiment_satisfied;
  static const IconData story_arc = Icons.auto_stories;
  static const IconData memory_lane = Icons.route;
  static const IconData smart_sort = Icons.sort;
  static const IconData context_aware = Icons.lightbulb;
}

/// Animation Durations
class LuminaAnimations {
  static const Duration instant = Duration(milliseconds: 100);
  static const Duration quick = Duration(milliseconds: 200);
  static const Duration smooth = Duration(milliseconds: 300);
  static const Duration fluid = Duration(milliseconds: 500);
  static const Duration slow = Duration(milliseconds: 800);
  static const Duration epic = Duration(milliseconds: 1200);
}
