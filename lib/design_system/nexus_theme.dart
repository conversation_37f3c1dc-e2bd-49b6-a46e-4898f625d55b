import 'package:flutter/material.dart';
import 'dart:math' as math;

/// NEXUS Design System - Revolutionary 3D Memory Ecosystem
/// Embraces complexity, depth, and emotional engagement
class NexusTheme {
  // === VIBRANT ENERGY PALETTE ===
  static const Color cosmicPurple = Color(0xFF6B46C1);
  static const Color electricBlue = Color(0xFF0EA5E9);
  static const Color neonGreen = Color(0xFF10B981);
  static const Color sunsetOrange = Color(0xFFEA580C);
  static const Color magentaFlare = Color(0xFFDB2777);
  static const Color goldenAura = Color(0xFFF59E0B);
  static const Color crimsonPulse = Color(0xFFDC2626);
  static const Color aquaMist = Color(0xFF06B6D4);
  
  // === DIMENSIONAL BACKGROUNDS ===
  static const Color voidBlack = Color(0xFF0F0F23);
  static const Color deepSpace = Color(0xFF1E1B4B);
  static const Color nebulaPurple = Color(0xFF312E81);
  static const Color starField = Color(0xFF1F2937);
  
  // === LIGHT DIMENSION ===
  static const Color crystalWhite = Color(0xFFFEFEFE);
  static const Color pearlGray = Color(0xFFF8FAFC);
  static const Color silverMist = Color(0xFFE2E8F0);
  static const Color cloudBase = Color(0xFFCBD5E1);
  
  // === COMPLEX GRADIENTS ===
  static const LinearGradient cosmicStorm = LinearGradient(
    colors: [cosmicPurple, electricBlue, neonGreen],
    stops: [0.0, 0.5, 1.0],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const RadialGradient memoryBubble = RadialGradient(
    colors: [magentaFlare, cosmicPurple, voidBlack],
    stops: [0.0, 0.7, 1.0],
    center: Alignment.center,
    radius: 1.5,
  );
  
  static const SweepGradient timeSpiral = SweepGradient(
    colors: [
      electricBlue,
      neonGreen,
      goldenAura,
      sunsetOrange,
      magentaFlare,
      cosmicPurple,
      electricBlue,
    ],
    stops: [0.0, 0.16, 0.33, 0.5, 0.66, 0.83, 1.0],
    center: Alignment.center,
  );
  
  static const LinearGradient emotionWave = LinearGradient(
    colors: [aquaMist, electricBlue, cosmicPurple, magentaFlare],
    stops: [0.0, 0.3, 0.7, 1.0],
    begin: Alignment(-1.0, -1.0),
    end: Alignment(1.0, 1.0),
  );
  
  // === LAYERED TYPOGRAPHY ===
  static const String primaryFont = 'Orbitron'; // Futuristic
  static const String secondaryFont = 'Exo 2'; // Modern Tech
  static const String accentFont = 'Rajdhani'; // Sharp & Bold
  
  static const TextStyle dimensionalTitle = TextStyle(
    fontFamily: primaryFont,
    fontSize: 42,
    fontWeight: FontWeight.w900,
    letterSpacing: 2.0,
    height: 1.1,
    shadows: [
      Shadow(
        color: Color(0x80DB2777),
        blurRadius: 20,
        offset: Offset(0, 0),
      ),
      Shadow(
        color: Color(0x400EA5E9),
        blurRadius: 40,
        offset: Offset(5, 5),
      ),
    ],
  );
  
  static const TextStyle memoryLabel = TextStyle(
    fontFamily: secondaryFont,
    fontSize: 18,
    fontWeight: FontWeight.w700,
    letterSpacing: 1.2,
    height: 1.3,
  );
  
  static const TextStyle quantumText = TextStyle(
    fontFamily: accentFont,
    fontSize: 14,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.8,
    height: 1.4,
  );
  
  static const TextStyle whisperText = TextStyle(
    fontFamily: secondaryFont,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.2,
    color: Color(0x80FFFFFF),
  );
  
  // === DIMENSIONAL SPACING ===
  static const double atomicUnit = 2.0;
  static const double quantumSpace = 6.0;
  static const double molecularSpace = 12.0;
  static const double cellularSpace = 20.0;
  static const double organicSpace = 32.0;
  static const double cosmicSpace = 52.0;
  static const double universalSpace = 84.0;
  static const double infiniteSpace = 136.0;
  
  // === COMPLEX SHAPES ===
  static const double bubbleRadius = 24.0;
  static const double orbRadius = 16.0;
  static const double crystalRadius = 8.0;
  static const double infiniteRadius = 9999.0;
  
  // === MULTI-LAYERED SHADOWS ===
  static const List<BoxShadow> dimensionalShadow = [
    BoxShadow(
      color: Color(0x40000000),
      blurRadius: 30,
      offset: Offset(0, 15),
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x20DB2777),
      blurRadius: 60,
      offset: Offset(0, 0),
      spreadRadius: 10,
    ),
    BoxShadow(
      color: Color(0x100EA5E9),
      blurRadius: 100,
      offset: Offset(0, 0),
      spreadRadius: 20,
    ),
  ];
  
  static const List<BoxShadow> memoryGlow = [
    BoxShadow(
      color: Color(0x6010B981),
      blurRadius: 40,
      offset: Offset(0, 0),
      spreadRadius: 5,
    ),
    BoxShadow(
      color: Color(0x300EA5E9),
      blurRadius: 80,
      offset: Offset(0, 0),
      spreadRadius: 15,
    ),
  ];
  
  // === COMPLEX ANIMATIONS ===
  static const Curve organicCurve = Curves.easeInOutCubic;
  static const Curve pulseCurve = Curves.elasticOut;
  static const Curve warpCurve = Curves.easeInOutBack;
  static const Curve quantumCurve = Curves.bounceOut;
  
  // === THEME CONFIGURATIONS ===
  static ThemeData get voidTheme => ThemeData(
    brightness: Brightness.dark,
    primaryColor: cosmicPurple,
    scaffoldBackgroundColor: voidBlack,
    cardColor: deepSpace,
    dividerColor: nebulaPurple,
    textTheme: const TextTheme(
      displayLarge: dimensionalTitle,
      displayMedium: memoryLabel,
      bodyLarge: quantumText,
      bodyMedium: whisperText,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      iconTheme: IconThemeData(color: crystalWhite),
    ),
  );
  
  static ThemeData get crystalTheme => ThemeData(
    brightness: Brightness.light,
    primaryColor: electricBlue,
    scaffoldBackgroundColor: crystalWhite,
    cardColor: pearlGray,
    dividerColor: silverMist,
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontFamily: primaryFont,
        fontSize: 42,
        fontWeight: FontWeight.w900,
        letterSpacing: 2.0,
        height: 1.1,
        color: voidBlack,
        shadows: [
          Shadow(
            color: Color(0x400EA5E9),
            blurRadius: 20,
            offset: Offset(0, 0),
          ),
        ],
      ),
      displayMedium: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 18,
        fontWeight: FontWeight.w700,
        letterSpacing: 1.2,
        height: 1.3,
        color: deepSpace,
      ),
      bodyLarge: TextStyle(
        fontFamily: accentFont,
        fontSize: 14,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.8,
        height: 1.4,
        color: starField,
      ),
      bodyMedium: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        height: 1.2,
        color: Color(0x80000000),
      ),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      iconTheme: IconThemeData(color: voidBlack),
    ),
  );
}

/// Revolutionary Icon System
class NexusIcons {
  // Memory Ecosystem Icons
  static const IconData memoryBubble = Icons.bubble_chart;
  static const IconData timeSpiral = Icons.timeline;
  static const IconData emotionCloud = Icons.cloud;
  static const IconData storyArc = Icons.auto_stories;
  static const IconData dimensionalView = Icons.view_in_ar;
  static const IconData quantumZoom = Icons.zoom_in;
  static const IconData neuralNetwork = Icons.hub;
  static const IconData cosmicSearch = Icons.travel_explore;
  static const IconData memoryLane = Icons.route;
  static const IconData pulseHeart = Icons.favorite;
  static const IconData voidPortal = Icons.radio_button_unchecked;
  static const IconData crystalPrism = Icons.diamond;
  static const IconData waveForm = Icons.graphic_eq;
  static const IconData particleField = Icons.scatter_plot;
  static const IconData energyFlow = Icons.electric_bolt;
  static const IconData dimensionShift = Icons.flip;
  static const IconData memoryMerge = Icons.merge;
  static const IconData timeWarp = Icons.access_time;
  static const IconData emotionScan = Icons.psychology;
  static const IconData storyWeave = Icons.auto_fix_high;
}

/// Complex Animation System
class NexusAnimations {
  static const Duration quantum = Duration(milliseconds: 150);
  static const Duration molecular = Duration(milliseconds: 300);
  static const Duration cellular = Duration(milliseconds: 500);
  static const Duration organic = Duration(milliseconds: 800);
  static const Duration cosmic = Duration(milliseconds: 1200);
  static const Duration universal = Duration(milliseconds: 2000);
  static const Duration infinite = Duration(milliseconds: 3000);
  
  // Breathing animation for memory bubbles
  static Animation<double> breathingScale(AnimationController controller) {
    return Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: controller, curve: Curves.easeInOut),
    );
  }
  
  // Pulsing glow for active elements
  static Animation<double> pulseGlow(AnimationController controller) {
    return Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: controller, curve: Curves.easeInOut),
    );
  }
  
  // Floating motion for memory bubbles
  static Animation<Offset> floatingMotion(AnimationController controller) {
    return Tween<Offset>(
      begin: const Offset(0, -5),
      end: const Offset(0, 5),
    ).animate(CurvedAnimation(parent: controller, curve: Curves.easeInOut));
  }
}
