import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:simple_gallery/screens/albums_screen.dart';
import 'package:simple_gallery/screens/favorites_screen.dart';
import 'package:simple_gallery/screens/hidden_screen.dart';
import 'package:simple_gallery/screens/home_screen.dart';
import 'package:simple_gallery/screens/settings_screen.dart';

import 'providers/gallery_provider.dart';
import 'providers/security_provider.dart';
import 'providers/theme_provider.dart';
import 'screens/splash_screen.dart';
import 'utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize mobile ads
  // await MobileAds.instance.initialize();
  
  // Request permissions
  await _requestPermissions();
  
  runApp(const SimpleGalleryApp());
}

Future<void> _requestPermissions() async {
  await Permission.photos.request();
  await Permission.storage.request();
  await Permission.camera.request();
}

class SimpleGalleryApp extends StatelessWidget {
  const SimpleGalleryApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => GalleryProvider()),
        ChangeNotifierProvider(create: (_) => SecurityProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Simple Gallery',
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: Colors.blue,
              brightness: themeProvider.isDarkMode ? Brightness.dark : Brightness.light,
              fontFamily: 'Roboto',
              appBarTheme: AppBarTheme(
                backgroundColor: themeProvider.isDarkMode ? Colors.grey[900] : Colors.white,
                foregroundColor: themeProvider.isDarkMode ? Colors.white : Colors.black,
                elevation: 0,
              ),
              scaffoldBackgroundColor: themeProvider.isDarkMode ? Colors.black : Colors.grey[50],
              // cardTheme: CardTheme(
              //   color: themeProvider.isDarkMode ? Colors.grey[900] : Colors.white,
              //   elevation: 2,
              // ),
            ),
            home: const SplashScreen(),
            routes: {
              '/home': (context) => const HomeScreen(),
              '/albums': (context) => const AlbumsScreen(),
              '/favorites': (context) => const FavoritesScreen(),
              '/hidden': (context) => const HiddenScreen(),
              '/settings': (context) => const SettingsScreen(),
            },
          );
        },
      ),
    );
  }
}
