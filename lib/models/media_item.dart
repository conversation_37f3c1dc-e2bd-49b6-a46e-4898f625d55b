import 'package:flutter/animation.dart';
import 'package:photo_manager/photo_manager.dart';

enum MediaType { image, video }

class MediaItem {
  final String id;
  final String path;
  final String name;
  final MediaType type;
  final DateTime createTime;
  final DateTime modifiedTime;
  final int size;
  final int width;
  final int height;
  final int duration; // For videos
  final String? albumId;
  final bool isFavorite;
  final bool isHidden;
  final bool isDeleted;
  final AssetEntity? assetEntity;

  MediaItem({
    required this.id,
    required this.path,
    required this.name,
    required this.type,
    required this.createTime,
    required this.modifiedTime,
    required this.size,
    required this.width,
    required this.height,
    this.duration = 0,
    this.albumId,
    this.isFavorite = false,
    this.isHidden = false,
    this.isDeleted = false,
    this.assetEntity,
  });

  factory MediaItem.fromAssetEntity(AssetEntity asset) {
    return MediaItem(
      id: asset.id,
      path: asset.relativePath ?? asset.id,
      name: asset.title ?? 'Unknown',
      type: asset.type == AssetType.image ? MediaType.image : MediaType.video,
      createTime: asset.createDateTime,
      modifiedTime: asset.modifiedDateTime,
      size: asset.size,
      width: asset.width,
      height: asset.height,
      duration: asset.duration,
      albumId: asset.id,
      assetEntity: asset,
    );
  }

  MediaItem copyWith({
    String? id,
    String? path,
    String? name,
    MediaType? type,
    DateTime? createTime,
    DateTime? modifiedTime,
    int? size,
    int? width,
    int? height,
    int? duration,
    String? albumId,
    bool? isFavorite,
    bool? isHidden,
    bool? isDeleted,
    AssetEntity? assetEntity,
  }) {
    return MediaItem(
      id: id ?? this.id,
      path: path ?? this.path,
      name: name ?? this.name,
      type: type ?? this.type,
      createTime: createTime ?? this.createTime,
      modifiedTime: modifiedTime ?? this.modifiedTime,
      size: size ?? this.size,
      width: width ?? this.width,
      height: height ?? this.height,
      duration: duration ?? this.duration,
      albumId: albumId ?? this.albumId,
      isFavorite: isFavorite ?? this.isFavorite,
      isHidden: isHidden ?? this.isHidden,
      isDeleted: isDeleted ?? this.isDeleted,
      assetEntity: assetEntity ?? this.assetEntity,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'path': path,
      'name': name,
      'type': type.index,
      'createTime': createTime.millisecondsSinceEpoch,
      'modifiedTime': modifiedTime.millisecondsSinceEpoch,
      'size': size,
      'width': width,
      'height': height,
      'duration': duration,
      'albumId': albumId,
      'isFavorite': isFavorite,
      'isHidden': isHidden,
      'isDeleted': isDeleted,
    };
  }

  factory MediaItem.fromJson(Map<String, dynamic> json) {
    return MediaItem(
      id: json['id'],
      path: json['path'],
      name: json['name'],
      type: MediaType.values[json['type']],
      createTime: DateTime.fromMillisecondsSinceEpoch(json['createTime']),
      modifiedTime: DateTime.fromMillisecondsSinceEpoch(json['modifiedTime']),
      size: json['size'],
      width: json['width'],
      height: json['height'],
      duration: json['duration'] ?? 0,
      albumId: json['albumId'],
      isFavorite: json['isFavorite'] ?? false,
      isHidden: json['isHidden'] ?? false,
      isDeleted: json['isDeleted'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MediaItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MediaItem(id: $id, name: $name, type: $type)';
  }
} 