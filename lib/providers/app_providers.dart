import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/contact_model.dart';
import '../models/call_log_model.dart';
import '../services/contact_service.dart';
import '../services/call_log_service.dart';
import '../services/permission_service.dart';

// Onboarding Provider
final onboardingProvider = StateNotifierProvider<OnboardingNotifier, bool>((ref) {
  return OnboardingNotifier();
});

class OnboardingNotifier extends StateNotifier<bool> {
  OnboardingNotifier() : super(false) {
    loadOnboardingStatus();
  }

  Future<void> _loadOnboardingStatus() async {
    final prefs = await SharedPreferences.getInstance();
    state = prefs.getBool('onboarding_completed') ?? false;
  }

  Future<void> completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_completed', true);
    state = true;
  }

  Future<void> resetOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_completed', false);
    state = false;
  }
}

// Permission Provider
final permissionProvider = StateNotifierProvider<PermissionNotifier, PermissionState>((ref) {
  return PermissionNotifier();
});

class PermissionState {
  final bool hasPhonePermission;
  final bool hasContactsPermission;
  final bool hasCallLogPermission;
  final bool isLoading;

  const PermissionState({
    this.hasPhonePermission = false,
    this.hasContactsPermission = false,
    this.hasCallLogPermission = false,
    this.isLoading = false,
  });

  PermissionState copyWith({
    bool? hasPhonePermission,
    bool? hasContactsPermission,
    bool? hasCallLogPermission,
    bool? isLoading,
  }) {
    return PermissionState(
      hasPhonePermission: hasPhonePermission ?? this.hasPhonePermission,
      hasContactsPermission: hasContactsPermission ?? this.hasContactsPermission,
      hasCallLogPermission: hasCallLogPermission ?? this.hasCallLogPermission,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get hasAllPermissions => hasPhonePermission && hasContactsPermission && hasCallLogPermission;
}

class PermissionNotifier extends StateNotifier<PermissionState> {
  PermissionNotifier() : super(const PermissionState()) {
    checkPermissions();
  }

  final _permissionService = PermissionService();

  Future<void> checkPermissions() async {
    state = state.copyWith(isLoading: true);
    
    final phonePermission = await _permissionService.hasPhonePermission();
    final contactsPermission = await _permissionService.hasContactsPermission();
    final callLogPermission = await _permissionService.hasCallLogPermission();
    
    state = state.copyWith(
      hasPhonePermission: phonePermission,
      hasContactsPermission: contactsPermission,
      hasCallLogPermission: callLogPermission,
      isLoading: false,
    );
  }

  Future<bool> requestAllPermissions() async {
    state = state.copyWith(isLoading: true);
    
    final result = await _permissionService.requestAllPermissions();
    await checkPermissions();
    
    return result;
  }

  Future<bool> requestPhonePermission() async {
    final result = await _permissionService.requestPhonePermission();
    await checkPermissions();
    return result;
  }

  Future<bool> requestContactsPermission() async {
    final result = await _permissionService.requestContactsPermission();
    await checkPermissions();
    return result;
  }

  Future<bool> requestCallLogPermission() async {
    final result = await _permissionService.requestCallLogPermission();
    await checkPermissions();
    return result;
  }
}

// Contacts Provider
final contactsProvider = StateNotifierProvider<ContactsNotifier, AsyncValue<List<ContactModel>>>((ref) {
  return ContactsNotifier();
});

class ContactsNotifier extends StateNotifier<AsyncValue<List<ContactModel>>> {
  ContactsNotifier() : super(const AsyncValue.loading()) {
    loadContacts();
  }

  final _contactService = ContactService();

  Future<void> loadContacts() async {
    state = const AsyncValue.loading();
    try {
      final contacts = await _contactService.getAllContacts();
      state = AsyncValue.data(contacts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addContact(ContactModel contact) async {
    try {
      await _contactService.addContact(contact);
      await loadContacts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateContact(ContactModel contact) async {
    try {
      await _contactService.updateContact(contact);
      await loadContacts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteContact(String contactId) async {
    try {
      await _contactService.deleteContact(contactId);
      await loadContacts();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  List<ContactModel> searchContacts(String query) {
    return state.when(
      data: (contacts) => _contactService.searchContacts(contacts, query),
      loading: () => [],
      error: (_, __) => [],
    );
  }
}

// Favorites Provider
final favoritesProvider = StateNotifierProvider<FavoritesNotifier, AsyncValue<List<ContactModel>>>((ref) {
  return FavoritesNotifier();
});

class FavoritesNotifier extends StateNotifier<AsyncValue<List<ContactModel>>> {
  FavoritesNotifier() : super(const AsyncValue.loading()) {
    loadFavorites();
  }

  final _contactService = ContactService();

  Future<void> loadFavorites() async {
    state = const AsyncValue.loading();
    try {
      final favorites = await _contactService.getFavoriteContacts();
      state = AsyncValue.data(favorites);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addToFavorites(ContactModel contact) async {
    try {
      await _contactService.addToFavorites(contact.id);
      await loadFavorites();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> removeFromFavorites(String contactId) async {
    try {
      await _contactService.removeFromFavorites(contactId);
      await loadFavorites();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Call Log Provider
final callLogProvider = StateNotifierProvider<CallLogNotifier, AsyncValue<List<CallLogModel>>>((ref) {
  return CallLogNotifier();
});

class CallLogNotifier extends StateNotifier<AsyncValue<List<CallLogModel>>> {
  CallLogNotifier() : super(const AsyncValue.loading()) {
    loadCallLogs();
  }

  final _callLogService = CallLogService();

  Future<void> loadCallLogs() async {
    state = const AsyncValue.loading();
    try {
      final callLogs = await _callLogService.getCallLogs();
      state = AsyncValue.data(callLogs);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addCallLog(CallLogModel callLog) async {
    try {
      await _callLogService.addCallLog(callLog);
      await loadCallLogs();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteCallLog(String callLogId) async {
    try {
      await _callLogService.deleteCallLog(callLogId);
      await loadCallLogs();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> clearAllCallLogs() async {
    try {
      await _callLogService.clearAllCallLogs();
      await loadCallLogs();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  List<CallLogModel> searchCallLogs(String query) {
    return state.when(
      data: (callLogs) => _callLogService.searchCallLogs(callLogs, query),
      loading: () => [],
      error: (_, __) => [],
    );
  }
}

// Search Provider
final searchProvider = StateNotifierProvider<SearchNotifier, String>((ref) {
  return SearchNotifier();
});

class SearchNotifier extends StateNotifier<String> {
  SearchNotifier() : super('');

  void updateQuery(String query) {
    state = query;
  }

  void clearQuery() {
    state = '';
  }
}

// Current Tab Provider
final currentTabProvider = StateNotifierProvider<CurrentTabNotifier, int>((ref) {
  return CurrentTabNotifier();
});

class CurrentTabNotifier extends StateNotifier<int> {
  CurrentTabNotifier() : super(0);

  void setTab(int index) {
    state = index;
  }
}

// Banner Visibility Provider
final bannerVisibilityProvider = StateNotifierProvider<BannerVisibilityNotifier, Map<String, bool>>((ref) {
  return BannerVisibilityNotifier();
});

class BannerVisibilityNotifier extends StateNotifier<Map<String, bool>> {
  BannerVisibilityNotifier() : super({
    'contacts_enrichment': true,
    'caller_id': true,
    'spam_protection': true,
  });

  void hideBanner(String bannerId) {
    state = {...state, bannerId: false};
  }

  void showBanner(String bannerId) {
    state = {...state, bannerId: true};
  }

  void resetBanners() {
    state = {
      'contacts_enrichment': true,
      'caller_id': true,
      'spam_protection': true,
    };
  }
}
