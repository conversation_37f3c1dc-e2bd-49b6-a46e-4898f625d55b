import 'package:flutter/foundation.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../models/media_item.dart';
import '../models/album.dart';
import '../utils/constants.dart';

enum SortOrder { date, name, size, type }

class GalleryProvider with ChangeNotifier {
  List<MediaItem> _allMedia = [];
  List<MediaItem> _filteredMedia = [];
  List<Album> _albums = [];
  List<MediaItem> _favorites = [];
  List<MediaItem> _hidden = [];
  List<MediaItem> _trash = [];
  Map<String, List<MediaItem>> _albumMediaMap = {};

  bool _isLoading = false;
  bool _hasPermission = false;
  SortOrder _sortOrder = SortOrder.date;
  bool _isSelectionMode = false;
  Set<String> _selectedItems = {};
  int _gridColumns = AppConstants.defaultGridCrossAxisCount;

  // Pagination variables
  static const int _pageSize = 50; // Load 50 items per page
  int _currentPage = 0;
  bool _hasMoreMedia = true;
  bool _isLoadingMore = false;
  AssetPathEntity? _recentAlbum;

  // Getters
  List<MediaItem> get allMedia => _allMedia;
  List<MediaItem> get filteredMedia => _filteredMedia;
  List<Album> get albums => _albums;
  List<MediaItem> get favorites => _favorites;
  List<MediaItem> get hidden => _hidden;
  List<MediaItem> get trash => _trash;
  
  bool get isLoading => _isLoading;
  bool get hasPermission => _hasPermission;
  SortOrder get sortOrder => _sortOrder;
  bool get isSelectionMode => _isSelectionMode;
  Set<String> get selectedItems => _selectedItems;
  int get gridColumns => _gridColumns;
  int get selectedCount => _selectedItems.length;

  GalleryProvider() {
    _loadSettings();
    _loadFavorites();
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _sortOrder = SortOrder.values[prefs.getInt(AppConstants.sortOrderKey) ?? 0];
    _gridColumns = prefs.getInt(AppConstants.gridColumnsKey) ?? AppConstants.defaultGridCrossAxisCount;
    notifyListeners();
  }

  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(AppConstants.sortOrderKey, _sortOrder.index);
    await prefs.setInt(AppConstants.gridColumnsKey, _gridColumns);
  }

  // Load favorites from SharedPreferences
  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = prefs.getString(AppConstants.favoritesKey);
    if (favoritesJson != null) {
      final List<dynamic> favoritesList = json.decode(favoritesJson);
      _favorites = favoritesList.map((json) => MediaItem.fromJson(json)).toList();
    }
  }

  // Save favorites to SharedPreferences
  Future<void> _saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = json.encode(_favorites.map((item) => item.toJson()).toList());
    await prefs.setString(AppConstants.favoritesKey, favoritesJson);
  }

  // Request permissions and load media
  Future<void> requestPermissionsAndLoad() async {
    _isLoading = true;
    notifyListeners();

    try {
      final permission = await PhotoManager.requestPermissionExtend();
      _hasPermission = permission.isAuth;

      if (_hasPermission) {
        await _loadMedia();
        await _loadAlbums();
      }
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      _hasPermission = false;
    }

    _isLoading = false;
    notifyListeners();
  }

  // Load all media from device
  Future<void> _loadMedia() async {
    try {
      final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        type: RequestType.image | RequestType.video, // Explicitly only images and videos
        onlyAll: true, // Only get the "Recent" album to avoid duplicates
      );

      if (albums.isNotEmpty) {
        final AssetPathEntity recentAlbum = albums.first; // "Recent" album contains all media
        final List<AssetEntity> assets = await recentAlbum.getAssetListRange(
          start: 0,
          end: 10000, // Load up to 10000 items
        );

        // Filter only images and videos, exclude audio
        final filteredAssets = assets.where((asset) =>
          asset.type == AssetType.image || asset.type == AssetType.video
        ).toList();

        // Use Set to ensure uniqueness by asset ID
        final Set<String> seenIds = <String>{};
        final List<MediaItem> uniqueMediaItems = [];

        for (final asset in filteredAssets) {
          if (!seenIds.contains(asset.id)) {
            seenIds.add(asset.id);
            uniqueMediaItems.add(MediaItem.fromAssetEntity(asset, albumId: recentAlbum.id));
          }
        }

        _allMedia = uniqueMediaItems;
        _applyFilters();
      }
    } catch (e) {
      debugPrint('Error loading media: $e');
    }
  }

  // Load albums
  Future<void> _loadAlbums() async {
    try {
      final List<AssetPathEntity> albumPaths = await PhotoManager.getAssetPathList(
        type: RequestType.image | RequestType.video, // Explicitly only images and videos
      );

      // Create a map to store media items by album
      Map<String, List<MediaItem>> albumMediaMap = {};

      _albums = await Future.wait(
        albumPaths.map((path) async {
          // Get assets for this album
          final assets = await path.getAssetListRange(start: 0, end: 10000);
          final filteredAssets = assets.where((asset) =>
            asset.type == AssetType.image || asset.type == AssetType.video
          ).toList();

          // Create media items for this album
          final albumMediaItems = filteredAssets.map((asset) =>
            MediaItem.fromAssetEntity(asset, albumId: path.id)
          ).toList();

          // Store in map for album detail screens
          albumMediaMap[path.id] = albumMediaItems;

          return Album(
            id: path.id,
            name: path.name,
            itemCount: filteredAssets.length,
            createTime: DateTime.now(),
            modifiedTime: DateTime.now(),
          );
        }),
      );

      // Store album media map for getMediaByAlbum method
      _albumMediaMap = albumMediaMap;

    } catch (e) {
      debugPrint('Error loading albums: $e');
    }
  }

  // Apply filters and sorting
  void _applyFilters() {
    _filteredMedia = List.from(_allMedia);
    
    // Apply sorting
    switch (_sortOrder) {
      case SortOrder.date:
        _filteredMedia.sort((a, b) => b.createTime.compareTo(a.createTime));
        break;
      case SortOrder.name:
        _filteredMedia.sort((a, b) => a.name.compareTo(b.name));
        break;
      case SortOrder.size:
        _filteredMedia.sort((a, b) => b.size.compareTo(a.size));
        break;
      case SortOrder.type:
        _filteredMedia.sort((a, b) => a.type.index.compareTo(b.type.index));
        break;
    }
  }

  // Set sort order
  void setSortOrder(SortOrder order) {
    _sortOrder = order;
    _applyFilters();
    _saveSettings();
    notifyListeners();
  }

  // Set grid columns
  void setGridColumns(int columns) {
    _gridColumns = columns;
    _saveSettings();
    notifyListeners();
  }

  // Toggle selection mode
  void toggleSelectionMode() {
    _isSelectionMode = !_isSelectionMode;
    if (!_isSelectionMode) {
      _selectedItems.clear();
    }
    notifyListeners();
  }

  // Select/deselect item
  void toggleItemSelection(String itemId) {
    if (_selectedItems.contains(itemId)) {
      _selectedItems.remove(itemId);
    } else {
      _selectedItems.add(itemId);
    }
    notifyListeners();
  }

  // Select all items
  void selectAll() {
    _selectedItems = _filteredMedia.map((item) => item.id).toSet();
    notifyListeners();
  }

  // Deselect all items
  void deselectAll() {
    _selectedItems.clear();
    notifyListeners();
  }

  // Toggle favorite
  void toggleFavorite(MediaItem item) {
    final index = _favorites.indexWhere((favorite) => favorite.id == item.id);
    
    if (index != -1) {
      _favorites.removeAt(index);
    } else {
      _favorites.add(item);
    }
    
    _saveFavorites();
    notifyListeners();
  }

  // Check if item is favorite
  bool isFavorite(String itemId) {
    return _favorites.any((item) => item.id == itemId);
  }

  // Hide item
  void hideItem(MediaItem item) {
    final index = _allMedia.indexWhere((media) => media.id == item.id);
    if (index != -1) {
      _allMedia[index] = _allMedia[index].copyWith(isHidden: true);
      _hidden.add(_allMedia[index]);
      _applyFilters();
      notifyListeners();
    }
  }

  // Show hidden item
  void showHiddenItem(MediaItem item) {
    final index = _allMedia.indexWhere((media) => media.id == item.id);
    if (index != -1) {
      _allMedia[index] = _allMedia[index].copyWith(isHidden: false);
      _hidden.removeWhere((hidden) => hidden.id == item.id);
      _applyFilters();
      notifyListeners();
    }
  }

  // Delete items
  Future<void> deleteItems(List<String> itemIds) async {
    for (String id in itemIds) {
      final index = _allMedia.indexWhere((item) => item.id == id);
      if (index != -1) {
        final item = _allMedia[index];
        _allMedia[index] = item.copyWith(isDeleted: true);
        _trash.add(_allMedia[index]);
        
        // Remove from favorites if present
        _favorites.removeWhere((favorite) => favorite.id == id);
      }
    }
    
    _selectedItems.clear();
    _isSelectionMode = false;
    _applyFilters();
    _saveFavorites();
    notifyListeners();
  }

  // Restore items from trash
  void restoreItems(List<String> itemIds) {
    for (String id in itemIds) {
      final index = _allMedia.indexWhere((item) => item.id == id);
      if (index != -1) {
        _allMedia[index] = _allMedia[index].copyWith(isDeleted: false);
        _trash.removeWhere((trash) => trash.id == id);
      }
    }
    
    _applyFilters();
    notifyListeners();
  }

  // Permanently delete items
  void permanentlyDeleteItems(List<String> itemIds) {
    for (String id in itemIds) {
      _allMedia.removeWhere((item) => item.id == id);
      _trash.removeWhere((trash) => trash.id == id);
      _favorites.removeWhere((favorite) => favorite.id == id);
      _hidden.removeWhere((hidden) => hidden.id == id);
    }
    
    _applyFilters();
    _saveFavorites();
    notifyListeners();
  }

  // Get media by album
  List<MediaItem> getMediaByAlbum(String albumId) {
    return _albumMediaMap[albumId] ?? [];
  }

  // Refresh media
  Future<void> refresh() async {
    await requestPermissionsAndLoad();
  }
} 