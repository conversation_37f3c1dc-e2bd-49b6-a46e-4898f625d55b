import 'package:flutter/foundation.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/constants.dart';

enum SecurityType { none, pin, pattern, fingerprint }

class SecurityProvider with ChangeNotifier {
  final LocalAuthentication _localAuth = LocalAuthentication();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  SecurityType _securityType = SecurityType.none;
  bool _isAuthenticated = false;
  bool _isLocked = false;
  String? _pin;
  List<int>? _pattern;
  bool _fingerprintEnabled = false;
  bool _isHiddenFolderEnabled = false;

  // Getters
  SecurityType get securityType => _securityType;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLocked => _isLocked;
  bool get fingerprintEnabled => _fingerprintEnabled;
  bool get isHiddenFolderEnabled => _isHiddenFolderEnabled;
  bool get hasSecurity => _securityType != SecurityType.none;

  SecurityProvider() {
    _loadSecuritySettings();
  }

  // Load security settings
  Future<void> _loadSecuritySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _securityType = SecurityType.values[prefs.getInt('security_type') ?? 0];
      _fingerprintEnabled = prefs.getBool('fingerprint_enabled') ?? false;
      _isHiddenFolderEnabled = prefs.getBool('hidden_folder_enabled') ?? false;
      
      // Load PIN from secure storage
      _pin = await _secureStorage.read(key: AppConstants.securityPinKey);
      
      // Load pattern from secure storage
      final patternString = await _secureStorage.read(key: AppConstants.securityPatternKey);
      if (patternString != null) {
        _pattern = patternString.split(',').map((e) => int.parse(e)).toList();
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading security settings: $e');
    }
  }

  // Save security settings
  Future<void> _saveSecuritySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('security_type', _securityType.index);
      await prefs.setBool('fingerprint_enabled', _fingerprintEnabled);
      await prefs.setBool('hidden_folder_enabled', _isHiddenFolderEnabled);
    } catch (e) {
      debugPrint('Error saving security settings: $e');
    }
  }

  // Set PIN
  Future<void> setPin(String pin) async {
    _pin = pin;
    _securityType = SecurityType.pin;
    await _secureStorage.write(key: AppConstants.securityPinKey, value: pin);
    await _saveSecuritySettings();
    notifyListeners();
  }

  // Verify PIN
  bool verifyPin(String pin) {
    if (_pin == pin) {
      _isAuthenticated = true;
      _isLocked = false;
      notifyListeners();
      return true;
    }
    return false;
  }

  // Set pattern
  Future<void> setPattern(List<int> pattern) async {
    _pattern = pattern;
    _securityType = SecurityType.pattern;
    final patternString = pattern.join(',');
    await _secureStorage.write(key: AppConstants.securityPatternKey, value: patternString);
    await _saveSecuritySettings();
    notifyListeners();
  }

  // Verify pattern
  bool verifyPattern(List<int> pattern) {
    if (_pattern != null && listEquals(_pattern, pattern)) {
      _isAuthenticated = true;
      _isLocked = false;
      notifyListeners();
      return true;
    }
    return false;
  }

  // Enable/disable fingerprint
  Future<void> setFingerprintEnabled(bool enabled) async {
    _fingerprintEnabled = enabled;
    if (enabled) {
      _securityType = SecurityType.fingerprint;
    }
    await _saveSecuritySettings();
    notifyListeners();
  }

  // Authenticate with fingerprint
  Future<bool> authenticateWithFingerprint() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      
      if (!isAvailable || !isDeviceSupported) {
        return false;
      }

      final List<BiometricType> availableBiometrics = await _localAuth.getAvailableBiometrics();
      
      if (availableBiometrics.contains(BiometricType.fingerprint)) {
        final bool didAuthenticate = await _localAuth.authenticate(
          localizedReason: 'Please authenticate to access the gallery',
          options: const AuthenticationOptions(
            stickyAuth: true,
            biometricOnly: true,
          ),
        );
        
        if (didAuthenticate) {
          _isAuthenticated = true;
          _isLocked = false;
          notifyListeners();
        }
        
        return didAuthenticate;
      }
    } catch (e) {
      debugPrint('Error authenticating with fingerprint: $e');
    }
    
    return false;
  }

  // Enable/disable hidden folder
  Future<void> setHiddenFolderEnabled(bool enabled) async {
    _isHiddenFolderEnabled = enabled;
    await _saveSecuritySettings();
    notifyListeners();
  }

  // Lock the app
  void lockApp() {
    _isLocked = true;
    _isAuthenticated = false;
    notifyListeners();
  }

  // Unlock the app
  void unlockApp() {
    _isLocked = false;
    _isAuthenticated = true;
    notifyListeners();
  }

  // Clear authentication
  void clearAuthentication() {
    _isAuthenticated = false;
    notifyListeners();
  }

  // Disable security
  Future<void> disableSecurity() async {
    _securityType = SecurityType.none;
    _pin = null;
    _pattern = null;
    _fingerprintEnabled = false;
    _isAuthenticated = false;
    _isLocked = false;
    
    // Clear secure storage
    await _secureStorage.delete(key: AppConstants.securityPinKey);
    await _secureStorage.delete(key: AppConstants.securityPatternKey);
    await _saveSecuritySettings();
    
    notifyListeners();
  }

  // Check if biometrics are available
  Future<bool> isBiometricsAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      
      if (!isAvailable || !isDeviceSupported) {
        return false;
      }

      final List<BiometricType> availableBiometrics = await _localAuth.getAvailableBiometrics();
      return availableBiometrics.contains(BiometricType.fingerprint);
    } catch (e) {
      debugPrint('Error checking biometrics availability: $e');
      return false;
    }
  }

  // Get security type string
  String getSecurityTypeString() {
    switch (_securityType) {
      case SecurityType.none:
        return 'None';
      case SecurityType.pin:
        return 'PIN';
      case SecurityType.pattern:
        return 'Pattern';
      case SecurityType.fingerprint:
        return 'Fingerprint';
    }
  }
} 