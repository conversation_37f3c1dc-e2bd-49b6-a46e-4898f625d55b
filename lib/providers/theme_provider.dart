import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/constants.dart';

enum ThemeMode { light, dark, system }

class ThemeProvider with ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  bool _isDarkMode = false;
  bool _isPremium = false;

  // Getters
  ThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _isDarkMode;
  bool get isPremium => _isPremium;

  ThemeProvider() {
    _loadThemeSettings();
  }

  // Load theme settings
  Future<void> _loadThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _themeMode = ThemeMode.values[prefs.getInt('theme_mode') ?? 0];
      _isPremium = prefs.getBool('is_premium') ?? false;
      
      // Determine if dark mode should be active
      _updateDarkMode();
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme settings: $e');
    }
  }

  // Save theme settings
  Future<void> _saveThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('theme_mode', _themeMode.index);
      await prefs.setBool('is_premium', _isPremium);
    } catch (e) {
      debugPrint('Error saving theme settings: $e');
    }
  }

  // Update dark mode based on theme mode
  void _updateDarkMode() {
    switch (_themeMode) {
      case ThemeMode.light:
        _isDarkMode = false;
        break;
      case ThemeMode.dark:
        _isDarkMode = true;
        break;
      case ThemeMode.system:
        // This would typically check the system theme
        // For now, we'll default to light mode
        _isDarkMode = false;
        break;
    }
  }

  // Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    _updateDarkMode();
    await _saveThemeSettings();
    notifyListeners();
  }

  // Toggle dark mode
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;
    await _saveThemeSettings();
    notifyListeners();
  }

  // Set premium status
  Future<void> setPremium(bool isPremium) async {
    _isPremium = isPremium;
    await _saveThemeSettings();
    notifyListeners();
  }

  // Get theme mode string
  String getThemeModeString() {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }
} 