import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/constants.dart';

enum AppThemeMode { light, dark, system }

class AppColors {
  // Vibrant Light Theme Colors
  static const Color lightPrimary = Color(0xFF6366F1); // Indigo
  static const Color lightSecondary = Color(0xFF8B5CF6); // Purple
  static const Color lightAccent = Color(0xFF06B6D4); // Cyan
  static const Color lightBackground = Color(0xFFFAFAFA);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightOnPrimary = Color(0xFFFFFFFF);
  static const Color lightOnSurface = Color(0xFF1F2937);

  // Vibrant Dark Theme Colors
  static const Color darkPrimary = Color(0xFF818CF8); // Light Indigo
  static const Color darkSecondary = Color(0xFFA78BFA); // Light Purple
  static const Color darkAccent = Color(0xFF22D3EE); // Light Cyan
  static const Color darkBackground = Color(0xFF0F0F0F);
  static const Color darkSurface = Color(0xFF1F1F1F);
  static const Color darkOnPrimary = Color(0xFF000000);
  static const Color darkOnSurface = Color(0xFFE5E7EB);

  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF6366F1),
    Color(0xFF8B5CF6),
  ];

  static const List<Color> accentGradient = [
    Color(0xFF06B6D4),
    Color(0xFF3B82F6),
  ];

  static const List<Color> darkGradient = [
    Color(0xFF1F2937),
    Color(0xFF111827),
  ];
}

class ThemeProvider with ChangeNotifier {
  AppThemeMode _themeMode = AppThemeMode.system;
  bool _isDarkMode = false;
  bool _isPremium = false;
  bool _useVibrantColors = true;

  // Getters
  AppThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _isDarkMode;
  bool get isPremium => _isPremium;
  bool get useVibrantColors => _useVibrantColors;

  ThemeProvider() {
    _loadThemeSettings();
  }

  // Load theme settings
  Future<void> _loadThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _themeMode = AppThemeMode.values[prefs.getInt('theme_mode') ?? 0];
      _isPremium = prefs.getBool('is_premium') ?? false;
      _useVibrantColors = prefs.getBool('use_vibrant_colors') ?? true;

      // Determine if dark mode should be active
      _updateDarkMode();

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme settings: $e');
    }
  }

  // Save theme settings
  Future<void> _saveThemeSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('theme_mode', _themeMode.index);
      await prefs.setBool('is_premium', _isPremium);
      await prefs.setBool('use_vibrant_colors', _useVibrantColors);
    } catch (e) {
      debugPrint('Error saving theme settings: $e');
    }
  }

  // Update dark mode based on theme mode
  void _updateDarkMode() {
    switch (_themeMode) {
      case AppThemeMode.light:
        _isDarkMode = false;
        break;
      case AppThemeMode.dark:
        _isDarkMode = true;
        break;
      case AppThemeMode.system:
        // This would typically check the system theme
        // For now, we'll default to light mode
        _isDarkMode = false;
        break;
    }
  }

  // Set theme mode
  Future<void> setThemeMode(AppThemeMode mode) async {
    _themeMode = mode;
    _updateDarkMode();
    await _saveThemeSettings();
    notifyListeners();
  }

  // Toggle dark mode
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    _themeMode = _isDarkMode ? AppThemeMode.dark : AppThemeMode.light;
    await _saveThemeSettings();
    notifyListeners();
  }

  // Toggle vibrant colors
  Future<void> setVibrantColors(bool vibrant) async {
    _useVibrantColors = vibrant;
    await _saveThemeSettings();
    notifyListeners();
  }

  // Set premium status
  Future<void> setPremium(bool isPremium) async {
    _isPremium = isPremium;
    await _saveThemeSettings();
    notifyListeners();
  }

  // Get theme mode string
  String getThemeModeString() {
    switch (_themeMode) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }

  // Get light theme data
  ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: _useVibrantColors ? AppColors.lightPrimary : Colors.blue,
      colorScheme: ColorScheme.light(
        primary: _useVibrantColors ? AppColors.lightPrimary : Colors.blue,
        secondary: _useVibrantColors ? AppColors.lightSecondary : Colors.blueAccent,
        surface: AppColors.lightSurface,
        background: AppColors.lightBackground,
        onPrimary: AppColors.lightOnPrimary,
        onSurface: AppColors.lightOnSurface,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.lightSurface,
        foregroundColor: AppColors.lightOnSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.lightOnSurface,
        ),
      ),
      cardTheme: CardThemeData(
        color: AppColors.lightSurface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _useVibrantColors ? AppColors.lightPrimary : Colors.blue,
          foregroundColor: AppColors.lightOnPrimary,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  // Get dark theme data
  ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: _useVibrantColors ? AppColors.darkPrimary : Colors.blueAccent,
      colorScheme: ColorScheme.dark(
        primary: _useVibrantColors ? AppColors.darkPrimary : Colors.blueAccent,
        secondary: _useVibrantColors ? AppColors.darkSecondary : Colors.blue,
        surface: AppColors.darkSurface,
        background: AppColors.darkBackground,
        onPrimary: AppColors.darkOnPrimary,
        onSurface: AppColors.darkOnSurface,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.darkSurface,
        foregroundColor: AppColors.darkOnSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.darkOnSurface,
        ),
      ),
      cardTheme: CardThemeData(
        color: AppColors.darkSurface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _useVibrantColors ? AppColors.darkPrimary : Colors.blueAccent,
          foregroundColor: AppColors.darkOnPrimary,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}