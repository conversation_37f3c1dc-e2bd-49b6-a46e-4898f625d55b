import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:math' as math;
import 'package:photo_manager/photo_manager.dart';
import 'package:image/image.dart' as img;

import '../models/media_item.dart';
import '../providers/theme_provider.dart';
import '../widgets/crop_widget.dart';

class AdvancedPhotoEditor extends StatefulWidget {
  final MediaItem mediaItem;

  const AdvancedPhotoEditor({
    super.key,
    required this.mediaItem,
  });

  @override
  State<AdvancedPhotoEditor> createState() => _AdvancedPhotoEditorState();
}

class _AdvancedPhotoEditorState extends State<AdvancedPhotoEditor>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _shimmerController;
  late AnimationController _toolbarController;

  late Animation<Offset> _slideAnimation;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _toolbarAnimation;

  Uint8List? _originalImageData;
  Uint8List? _editedImageData;
  ui.Image? _uiImage;

  bool _isLoading = true;
  bool _isProcessing = false;
  String _processingText = 'Loading...';

  // Editing modes
  EditingMode _currentMode = EditingMode.filters;
  String? _selectedFilter;

  // Manual adjustment values
  double _brightness = 0.0;
  double _contrast = 1.0;
  double _saturation = 1.0;
  double _highlights = 0.0;
  double _shadows = 0.0;
  double _warmth = 0.0;
  double _tint = 0.0;
  double _clarity = 0.0;
  double _vibrance = 0.0;
  double _exposure = 0.0;

  // Crop values
  double _rotation = 0.0;
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  bool _flipHorizontal = false;
  bool _flipVertical = false;
  Rect _cropRect = const Rect.fromLTWH(0.1, 0.1, 0.8, 0.8);

  // Transform controller for crop
  late TransformationController _transformController;

  @override
  void initState() {
    super.initState();
    _transformController = TransformationController();
    _setupAnimations();
    _loadImage();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _toolbarController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    _toolbarAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _toolbarController,
      curve: Curves.easeInOut,
    ));

    _slideController.forward();
    _toolbarController.forward();
  }

  Future<void> _loadImage() async {
    setState(() {
      _isLoading = true;
      _processingText = 'Loading image...';
    });

    try {
      if (widget.mediaItem.assetEntity != null) {
        final imageData = await widget.mediaItem.assetEntity!.originBytes;
        if (mounted && imageData != null) {
          // Decode image to UI Image for better processing
          final codec = await ui.instantiateImageCodec(imageData);
          final frame = await codec.getNextFrame();

          setState(() {
            _originalImageData = imageData;
            _editedImageData = imageData;
            _uiImage = frame.image;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _shimmerController.dispose();
    _toolbarController.dispose();
    _transformController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              _buildTopBar(),
              Expanded(
                child: _buildImageEditor(),
              ),
              _buildBottomControls(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          _buildTopButton(
            icon: Icons.arrow_back_ios_rounded,
            onTap: () => Navigator.pop(context),
          ),
          const Spacer(),
          Text(
            'Photo Editor',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          _buildTopButton(
            icon: Icons.done_rounded,
            onTap: _saveImage,
            isPrimary: true,
          ),
        ],
      ),
    );
  }

  Widget _buildTopButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isPrimary = false,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          gradient: isPrimary ? LinearGradient(
            colors: [AppColors.lightPrimary, AppColors.lightSecondary],
          ) : null,
          color: isPrimary ? null : Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: isPrimary ? [
            BoxShadow(
              color: AppColors.lightPrimary.withOpacity(0.4),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildImageEditor() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 3,
        ),
      );
    }

    if (_editedImageData == null) {
      return const Center(
        child: Icon(
          Icons.broken_image_rounded,
          color: Colors.white,
          size: 64,
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Center(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final size = math.min(constraints.maxWidth, constraints.maxHeight - 40);
            return Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _currentMode == EditingMode.crop
                    ? CropWidget(
                        imageData: _editedImageData!,
                        rotation: _rotation,
                        flipHorizontal: _flipHorizontal,
                        flipVertical: _flipVertical,
                        onCropChanged: (rect) {
                          setState(() {
                            _cropRect = rect;
                          });
                        },
                      )
                    : Stack(
                        fit: StackFit.expand,
                        children: [
                          _buildTransformableImage(),
                          if (_isProcessing) _buildImageShimmer(),
                        ],
                      ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTransformableImage() {
    return Transform.rotate(
      angle: _rotation * (math.pi / 180),
      child: Transform.scale(
        scaleX: _flipHorizontal ? -1 : 1,
        scaleY: _flipVertical ? -1 : 1,
        child: ColorFiltered(
          colorFilter: _buildColorMatrix(),
          child: _currentMode == EditingMode.crop
              ? InteractiveViewer(
                  transformationController: _transformController,
                  boundaryMargin: const EdgeInsets.all(0),
                  minScale: 0.8,
                  maxScale: 2.5,
                  constrained: false,
                  child: Image.memory(
                    _editedImageData!,
                    fit: BoxFit.contain,
                  ),
                )
              : Image.memory(
                  _editedImageData!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                ),
        ),
      ),
    );
  }

  Widget _buildImageShimmer() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.transparent,
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.4),
                Colors.white.withOpacity(0.1),
                Colors.transparent,
              ],
              stops: [
                math.max(0.0, _shimmerAnimation.value - 0.2),
                math.max(0.0, _shimmerAnimation.value - 0.1),
                _shimmerAnimation.value.clamp(0.0, 1.0),
                math.min(1.0, _shimmerAnimation.value + 0.1),
                math.min(1.0, _shimmerAnimation.value + 0.2),
              ],
            ),
          ),
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 14,
                    height: 14,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.lightPrimary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _processingText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCropOverlay() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.lightPrimary,
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // Grid lines
          CustomPaint(
            painter: CropGridPainter(),
            size: Size.infinite,
          ),
          // Corner handles
          ..._buildCropHandles(),
        ],
      ),
    );
  }

  List<Widget> _buildCropHandles() {
    return [
      // Top-left
      Positioned(
        top: -8,
        left: -8,
        child: _buildCropHandle(),
      ),
      // Top-right
      Positioned(
        top: -8,
        right: -8,
        child: _buildCropHandle(),
      ),
      // Bottom-left
      Positioned(
        bottom: -8,
        left: -8,
        child: _buildCropHandle(),
      ),
      // Bottom-right
      Positioned(
        bottom: -8,
        right: -8,
        child: _buildCropHandle(),
      ),
    ];
  }

  Widget _buildCropHandle() {
    return Container(
      width: 16,
      height: 16,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.lightPrimary,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 180),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.9),
            Colors.black,
          ],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 8),
          _buildModeSelector(),
          const SizedBox(height: 12),
          Flexible(
            child: _buildEditingControls(),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildModeSelector() {
    return Container(
      height: 44,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(22),
      ),
      child: Row(
        children: EditingMode.values.map((mode) {
          final isSelected = _currentMode == mode;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                setState(() {
                  _currentMode = mode;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  gradient: isSelected ? LinearGradient(
                    colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                  ) : null,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Text(
                    _getModeTitle(mode),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEditingControls() {
    switch (_currentMode) {
      case EditingMode.filters:
        return _buildFiltersControls();
      case EditingMode.adjust:
        return _buildAdjustControls();
      case EditingMode.crop:
        return _buildCropControls();
      case EditingMode.effects:
        return _buildEffectsControls();
    }
  }

  Widget _buildFiltersControls() {
    final filters = [
      {'name': 'Original', 'icon': Icons.image_outlined},
      {'name': 'Vintage', 'icon': Icons.camera_alt_outlined},
      {'name': 'B&W', 'icon': Icons.filter_b_and_w_outlined},
      {'name': 'Dramatic', 'icon': Icons.flash_on_outlined},
      {'name': 'Cool', 'icon': Icons.ac_unit_outlined},
      {'name': 'Warm', 'icon': Icons.wb_sunny_outlined},
      {'name': 'Soft', 'icon': Icons.blur_on_outlined},
      {'name': 'Vivid', 'icon': Icons.nature_outlined},
      {'name': 'Sepia', 'icon': Icons.palette_outlined},
      {'name': 'Neon', 'icon': Icons.highlight_outlined},
    ];

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['name'];

          return GestureDetector(
            onTap: () => _applyFilter(filter['name'] as String),
            child: Container(
              width: 70,
              margin: const EdgeInsets.only(right: 12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: isSelected ? LinearGradient(
                        colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                      ) : null,
                      color: isSelected ? null : Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                          ? Colors.transparent
                          : Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      filter['icon'] as IconData,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    filter['name'] as String,
                    style: TextStyle(
                      color: isSelected ? AppColors.lightPrimary : Colors.white,
                      fontSize: 10,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAdjustControls() {
    final adjustments = [
      {'name': 'Brightness', 'value': _brightness, 'min': -1.0, 'max': 1.0, 'icon': Icons.brightness_6_rounded},
      {'name': 'Contrast', 'value': _contrast - 1.0, 'min': -1.0, 'max': 1.0, 'icon': Icons.contrast_rounded},
      {'name': 'Saturation', 'value': _saturation - 1.0, 'min': -1.0, 'max': 1.0, 'icon': Icons.palette_rounded},
      {'name': 'Warmth', 'value': _warmth, 'min': -1.0, 'max': 1.0, 'icon': Icons.wb_sunny_rounded},
    ];

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: adjustments.length,
        itemBuilder: (context, index) {
          final adjustment = adjustments[index];
          return Container(
            width: 70,
            margin: const EdgeInsets.only(right: 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  adjustment['icon'] as IconData,
                  color: Colors.white,
                  size: 18,
                ),
                const SizedBox(height: 4),
                Text(
                  adjustment['name'] as String,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 6),
                Expanded(
                  child: RotatedBox(
                    quarterTurns: 3,
                    child: SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        activeTrackColor: AppColors.lightPrimary,
                        inactiveTrackColor: Colors.white.withOpacity(0.3),
                        thumbColor: AppColors.lightPrimary,
                        trackHeight: 2,
                        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                      ),
                      child: Slider(
                        value: (adjustment['value'] as double).clamp(
                          adjustment['min'] as double,
                          adjustment['max'] as double,
                        ),
                        min: adjustment['min'] as double,
                        max: adjustment['max'] as double,
                        onChanged: (value) {
                          HapticFeedback.selectionClick();
                          setState(() {
                            switch (adjustment['name']) {
                              case 'Brightness':
                                _brightness = value;
                                break;
                              case 'Contrast':
                                _contrast = value + 1.0;
                                break;
                              case 'Saturation':
                                _saturation = value + 1.0;
                                break;
                              case 'Warmth':
                                _warmth = value;
                                break;
                            }
                          });
                        },
                      ),
                    ),
                  ),
                ),
                Text(
                  (adjustment['value'] as double).toStringAsFixed(1),
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 9,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCropControls() {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Aspect ratio buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildAspectRatioButton('Free', null),
              _buildAspectRatioButton('1:1', 1.0),
              _buildAspectRatioButton('4:3', 4.0/3.0),
              _buildAspectRatioButton('16:9', 16.0/9.0),
            ],
          ),
          const SizedBox(height: 8),
          // Rotation slider
          Row(
            children: [
              Icon(Icons.rotate_left_rounded, color: Colors.white, size: 16),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: AppColors.lightPrimary,
                      inactiveTrackColor: Colors.white.withOpacity(0.3),
                      thumbColor: AppColors.lightPrimary,
                      trackHeight: 2,
                      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                    ),
                    child: Slider(
                      value: _rotation,
                      min: -45.0,
                      max: 45.0,
                      onChanged: (value) {
                        HapticFeedback.lightImpact();
                        setState(() => _rotation = value);
                      },
                    ),
                  ),
                ),
              ),
              Icon(Icons.rotate_right_rounded, color: Colors.white, size: 16),
            ],
          ),
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildCropButton(
                icon: Icons.flip_rounded,
                label: 'Flip H',
                isActive: _flipHorizontal,
                onTap: () {
                  HapticFeedback.mediumImpact();
                  setState(() => _flipHorizontal = !_flipHorizontal);
                },
              ),
              _buildCropButton(
                icon: Icons.flip_camera_android_rounded,
                label: 'Flip V',
                isActive: _flipVertical,
                onTap: () {
                  HapticFeedback.mediumImpact();
                  setState(() => _flipVertical = !_flipVertical);
                },
              ),
              _buildCropButton(
                icon: Icons.refresh_rounded,
                label: 'Reset',
                isActive: false,
                onTap: _resetCrop,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAspectRatioButton(String label, double? ratio) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // TODO: Apply aspect ratio to crop
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildCropButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          gradient: isActive ? LinearGradient(
            colors: [AppColors.lightPrimary, AppColors.lightSecondary],
          ) : null,
          color: isActive ? null : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isActive
              ? Colors.transparent
              : Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 16),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEffectsControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: const Center(
        child: Text(
          'Effects coming soon...',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildEffectSlider(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: 20),
      child: Column(
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppColors.lightPrimary,
                  inactiveTrackColor: Colors.white.withOpacity(0.3),
                  thumbColor: AppColors.lightPrimary,
                  overlayColor: AppColors.lightPrimary.withOpacity(0.2),
                ),
                child: Slider(
                  value: value,
                  min: min,
                  max: max,
                  onChanged: onChanged,
                ),
              ),
            ),
          ),
          Text(
            value.toStringAsFixed(1),
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }



  void _applyFilter(String filterName) async {
    setState(() {
      _isProcessing = true;
      _processingText = 'Applying $filterName...';
      _selectedFilter = filterName;
    });

    _shimmerController.repeat();

    // Apply filter adjustments with proper values
    await Future.delayed(const Duration(milliseconds: 800));

    setState(() {
      switch (filterName) {
        case 'Original':
          _brightness = 0.0;
          _contrast = 1.0;
          _saturation = 1.0;
          _warmth = 0.0;
          break;
        case 'Vintage':
          _brightness = 0.15;
          _contrast = 1.3;
          _saturation = 0.7;
          _warmth = 0.4;
          break;
        case 'B&W':
          _brightness = 0.0;
          _contrast = 1.2;
          _saturation = 0.0;
          _warmth = 0.0;
          break;
        case 'Dramatic':
          _brightness = -0.1;
          _contrast = 1.6;
          _saturation = 1.4;
          _warmth = 0.0;
          break;
        case 'Cool':
          _brightness = 0.05;
          _contrast = 1.1;
          _saturation = 0.9;
          _warmth = -0.4;
          break;
        case 'Warm':
          _brightness = 0.1;
          _contrast = 1.1;
          _saturation = 1.2;
          _warmth = 0.5;
          break;
        case 'Soft':
          _brightness = 0.2;
          _contrast = 0.8;
          _saturation = 0.9;
          _warmth = 0.2;
          break;
        case 'Vivid':
          _brightness = 0.1;
          _contrast = 1.4;
          _saturation = 1.6;
          _warmth = 0.1;
          break;
        case 'Sepia':
          _brightness = 0.1;
          _contrast = 1.1;
          _saturation = 0.3;
          _warmth = 0.6;
          break;
        case 'Neon':
          _brightness = 0.0;
          _contrast = 1.5;
          _saturation = 1.8;
          _warmth = -0.2;
          break;
      }
      _isProcessing = false;
    });

    _shimmerController.stop();
    HapticFeedback.mediumImpact();
  }

  void _resetCrop() {
    setState(() {
      _rotation = 0.0;
      _scale = 1.0;
      _offset = Offset.zero;
      _flipHorizontal = false;
      _flipVertical = false;
      _cropRect = const Rect.fromLTWH(0.1, 0.1, 0.8, 0.8);
      _transformController.value = Matrix4.identity();
    });

    // Crop widget will reset automatically with the new state

    HapticFeedback.mediumImpact();
  }

  IconData _getModeIcon(EditingMode mode) {
    switch (mode) {
      case EditingMode.filters:
        return Icons.palette_rounded;
      case EditingMode.adjust:
        return Icons.tune_rounded;
      case EditingMode.crop:
        return Icons.crop_rounded;
      case EditingMode.effects:
        return Icons.auto_fix_high_rounded;
    }
  }

  String _getModeTitle(EditingMode mode) {
    switch (mode) {
      case EditingMode.filters:
        return 'Filters';
      case EditingMode.adjust:
        return 'Adjust';
      case EditingMode.crop:
        return 'Crop';
      case EditingMode.effects:
        return 'Effects';
    }
  }

  ColorFilter _buildColorMatrix() {
    // Create comprehensive color matrix
    List<double> matrix = [
      _contrast, 0, 0, 0, _brightness * 255,
      0, _contrast, 0, 0, _brightness * 255,
      0, 0, _contrast, 0, _brightness * 255,
      0, 0, 0, 1, 0,
    ];

    // Apply saturation
    final satMatrix = _getSaturationMatrix(_saturation);
    matrix = _multiplyMatrices(matrix, satMatrix);

    // Apply warmth (temperature adjustment)
    if (_warmth != 0.0) {
      final warmthMatrix = _getWarmthMatrix(_warmth);
      matrix = _multiplyMatrices(matrix, warmthMatrix);
    }

    return ColorFilter.matrix(matrix);
  }

  List<double> _getSaturationMatrix(double saturation) {
    final lumR = 0.2126;
    final lumG = 0.7152;
    final lumB = 0.0722;

    final sr = (1 - saturation) * lumR;
    final sg = (1 - saturation) * lumG;
    final sb = (1 - saturation) * lumB;

    return [
      sr + saturation, sg, sb, 0, 0,
      sr, sg + saturation, sb, 0, 0,
      sr, sg, sb + saturation, 0, 0,
      0, 0, 0, 1, 0,
    ];
  }

  List<double> _getWarmthMatrix(double warmth) {
    // Warmth adjustment - affects red and blue channels
    final warmthFactor = warmth * 0.3;
    return [
      1 + warmthFactor, 0, 0, 0, 0,
      0, 1, 0, 0, 0,
      0, 0, 1 - warmthFactor, 0, 0,
      0, 0, 0, 1, 0,
    ];
  }

  List<double> _multiplyMatrices(List<double> a, List<double> b) {
    List<double> result = List.filled(20, 0);

    for (int i = 0; i < 4; i++) {
      for (int j = 0; j < 5; j++) {
        if (j < 4) {
          for (int k = 0; k < 4; k++) {
            result[i * 5 + j] += a[i * 5 + k] * b[k * 5 + j];
          }
        } else {
          result[i * 5 + j] = a[i * 5 + j] + b[i * 5 + j];
        }
      }
    }

    return result;
  }

  void _saveImage() async {
    setState(() {
      _isProcessing = true;
      _processingText = 'Saving image...';
    });

    _shimmerController.repeat();

    // Simulate save processing
    await Future.delayed(const Duration(milliseconds: 1500));

    _shimmerController.stop();

    HapticFeedback.mediumImpact();
    Navigator.pop(context);
  }
}

enum EditingMode {
  filters,
  adjust,
  crop,
  effects,
}

class CropGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.lightPrimary.withOpacity(0.6)
      ..strokeWidth = 1;

    // Vertical lines
    canvas.drawLine(
      Offset(size.width / 3, 0),
      Offset(size.width / 3, size.height),
      paint,
    );
    canvas.drawLine(
      Offset(size.width * 2 / 3, 0),
      Offset(size.width * 2 / 3, size.height),
      paint,
    );

    // Horizontal lines
    canvas.drawLine(
      Offset(0, size.height / 3),
      Offset(size.width, size.height / 3),
      paint,
    );
    canvas.drawLine(
      Offset(0, size.height * 2 / 3),
      Offset(size.width, size.height * 2 / 3),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
