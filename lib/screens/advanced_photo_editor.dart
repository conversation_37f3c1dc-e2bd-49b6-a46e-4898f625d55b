import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:math' as math;
import 'package:photo_manager/photo_manager.dart';

import '../models/media_item.dart';
import '../providers/theme_provider.dart';
import '../widgets/crop_editor.dart';
import '../widgets/manual_adjustments.dart';
import '../widgets/filter_gallery.dart';
import '../widgets/loading_overlay.dart';

class AdvancedPhotoEditor extends StatefulWidget {
  final MediaItem mediaItem;

  const AdvancedPhotoEditor({
    super.key,
    required this.mediaItem,
  });

  @override
  State<AdvancedPhotoEditor> createState() => _AdvancedPhotoEditorState();
}

class _AdvancedPhotoEditorState extends State<AdvancedPhotoEditor>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _toolbarController;
  late AnimationController _loadingController;
  
  late Animation<Offset> _slideAnimation;
  late Animation<double> _toolbarAnimation;
  late Animation<double> _loadingAnimation;
  
  Uint8List? _originalImageData;
  Uint8List? _editedImageData;
  bool _isLoading = true;
  bool _isProcessing = false;
  String _processingText = 'Loading...';
  
  // Editing modes
  EditingMode _currentMode = EditingMode.filters;
  
  // Manual adjustment values
  double _brightness = 0.0;
  double _contrast = 0.0;
  double _saturation = 0.0;
  double _highlights = 0.0;
  double _shadows = 0.0;
  double _warmth = 0.0;
  double _tint = 0.0;
  double _clarity = 0.0;
  double _vibrance = 0.0;
  double _exposure = 0.0;
  double _gamma = 0.0;
  double _hue = 0.0;
  
  // Crop values
  Rect? _cropRect;
  double _rotation = 0.0;
  bool _flipHorizontal = false;
  bool _flipVertical = false;
  
  // Effects
  double _vignette = 0.0;
  double _blur = 0.0;
  double _sharpen = 0.0;
  double _grain = 0.0;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadImage();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _toolbarController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _toolbarAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _toolbarController,
      curve: Curves.easeInOut,
    ));
    
    _loadingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingController,
      curve: Curves.easeInOut,
    ));
    
    _slideController.forward();
    _toolbarController.forward();
  }

  Future<void> _loadImage() async {
    setState(() {
      _isLoading = true;
      _processingText = 'Loading image...';
    });
    
    _loadingController.repeat();
    
    try {
      if (widget.mediaItem.assetEntity != null) {
        final imageData = await widget.mediaItem.assetEntity!.originBytes;
        if (mounted) {
          setState(() {
            _originalImageData = imageData;
            _editedImageData = imageData;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } finally {
      _loadingController.stop();
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _toolbarController.dispose();
    _loadingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: Colors.black,
      body: SlideTransition(
        position: _slideAnimation,
        child: Stack(
          children: [
            Column(
              children: [
                _buildTopBar(isDark),
                Expanded(
                  child: _buildImageEditor(isDark),
                ),
                _buildBottomControls(isDark),
              ],
            ),
            if (_isLoading || _isProcessing) 
              LoadingOverlay(
                isVisible: _isLoading || _isProcessing,
                text: _processingText,
                animation: _loadingAnimation,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar(bool isDark) {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.8),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          children: [
            _buildTopButton(
              icon: Icons.close_rounded,
              onTap: () => Navigator.pop(context),
            ),
            const Spacer(),
            Text(
              'Photo Editor',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w700,
                letterSpacing: 0.5,
              ),
            ),
            const Spacer(),
            _buildTopButton(
              icon: Icons.check_rounded,
              onTap: _saveImage,
              isPrimary: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isPrimary = false,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          gradient: isPrimary ? LinearGradient(
            colors: [AppColors.lightPrimary, AppColors.lightSecondary],
          ) : null,
          color: isPrimary ? null : Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: isPrimary ? [
            BoxShadow(
              color: AppColors.lightPrimary.withOpacity(0.4),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildImageEditor(bool isDark) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }

    if (_editedImageData == null) {
      return const Center(
        child: Icon(
          Icons.broken_image_rounded,
          color: Colors.white,
          size: 64,
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.6),
            blurRadius: 30,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Transform.rotate(
              angle: _rotation * (math.pi / 180),
              child: Transform.scale(
                scaleX: _flipHorizontal ? -1 : 1,
                scaleY: _flipVertical ? -1 : 1,
                child: ColorFiltered(
                  colorFilter: _buildColorMatrix(),
                  child: Image.memory(
                    _editedImageData!,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
            if (_currentMode == EditingMode.crop) _buildCropOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildCropOverlay() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.white,
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // Grid lines
          CustomPaint(
            painter: CropGridPainter(),
            size: Size.infinite,
          ),
          // Corner handles
          ..._buildCropHandles(),
        ],
      ),
    );
  }

  List<Widget> _buildCropHandles() {
    return [
      // Top-left
      Positioned(
        top: -8,
        left: -8,
        child: _buildCropHandle(),
      ),
      // Top-right
      Positioned(
        top: -8,
        right: -8,
        child: _buildCropHandle(),
      ),
      // Bottom-left
      Positioned(
        bottom: -8,
        left: -8,
        child: _buildCropHandle(),
      ),
      // Bottom-right
      Positioned(
        bottom: -8,
        right: -8,
        child: _buildCropHandle(),
      ),
    ];
  }

  Widget _buildCropHandle() {
    return Container(
      width: 16,
      height: 16,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.lightPrimary,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls(bool isDark) {
    return AnimatedBuilder(
      animation: _toolbarAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 100 * (1 - _toolbarAnimation.value)),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.9),
                  Colors.black,
                ],
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildModeSelector(),
                  const SizedBox(height: 20),
                  _buildEditingControls(),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildModeSelector() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: EditingMode.values.map((mode) {
          final isSelected = _currentMode == mode;
          return GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              setState(() {
                _currentMode = mode;
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                gradient: isSelected ? LinearGradient(
                  colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                ) : null,
                color: isSelected ? null : Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: isSelected 
                    ? Colors.transparent
                    : Colors.white.withOpacity(0.3),
                  width: 1,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: AppColors.lightPrimary.withOpacity(0.4),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ] : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getModeIcon(mode),
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getModeTitle(mode),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEditingControls() {
    switch (_currentMode) {
      case EditingMode.filters:
        return FilterGallery(
          onFilterSelected: _applyFilter,
          originalImage: _originalImageData,
        );
      case EditingMode.adjust:
        return ManualAdjustments(
          brightness: _brightness,
          contrast: _contrast,
          saturation: _saturation,
          highlights: _highlights,
          shadows: _shadows,
          warmth: _warmth,
          tint: _tint,
          clarity: _clarity,
          vibrance: _vibrance,
          exposure: _exposure,
          gamma: _gamma,
          hue: _hue,
          onChanged: _updateAdjustments,
        );
      case EditingMode.crop:
        return CropEditor(
          rotation: _rotation,
          flipHorizontal: _flipHorizontal,
          flipVertical: _flipVertical,
          onRotationChanged: (value) {
            setState(() => _rotation = value);
            HapticFeedback.lightImpact();
          },
          onFlipHorizontal: () {
            setState(() => _flipHorizontal = !_flipHorizontal);
            HapticFeedback.mediumImpact();
          },
          onFlipVertical: () {
            setState(() => _flipVertical = !_flipVertical);
            HapticFeedback.mediumImpact();
          },
          onReset: _resetCrop,
        );
      case EditingMode.effects:
        return _buildEffectsControls();
    }
  }

  Widget _buildEffectsControls() {
    return Container(
      height: 120,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        children: [
          _buildEffectSlider('Vignette', _vignette, 0.0, 1.0, (value) {
            setState(() => _vignette = value);
          }),
          _buildEffectSlider('Blur', _blur, 0.0, 10.0, (value) {
            setState(() => _blur = value);
          }),
          _buildEffectSlider('Sharpen', _sharpen, 0.0, 2.0, (value) {
            setState(() => _sharpen = value);
          }),
          _buildEffectSlider('Grain', _grain, 0.0, 1.0, (value) {
            setState(() => _grain = value);
          }),
        ],
      ),
    );
  }

  Widget _buildEffectSlider(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: 20),
      child: Column(
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppColors.lightPrimary,
                  inactiveTrackColor: Colors.white.withOpacity(0.3),
                  thumbColor: AppColors.lightPrimary,
                  overlayColor: AppColors.lightPrimary.withOpacity(0.2),
                ),
                child: Slider(
                  value: value,
                  min: min,
                  max: max,
                  onChanged: onChanged,
                ),
              ),
            ),
          ),
          Text(
            value.toStringAsFixed(1),
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getModeIcon(EditingMode mode) {
    switch (mode) {
      case EditingMode.filters:
        return Icons.palette_rounded;
      case EditingMode.adjust:
        return Icons.tune_rounded;
      case EditingMode.crop:
        return Icons.crop_rounded;
      case EditingMode.effects:
        return Icons.auto_fix_high_rounded;
    }
  }

  String _getModeTitle(EditingMode mode) {
    switch (mode) {
      case EditingMode.filters:
        return 'Filters';
      case EditingMode.adjust:
        return 'Adjust';
      case EditingMode.crop:
        return 'Crop';
      case EditingMode.effects:
        return 'Effects';
    }
  }

  void _applyFilter(String filterName) async {
    setState(() {
      _isProcessing = true;
      _processingText = 'Applying $filterName filter...';
    });

    _loadingController.repeat();

    // Apply filter adjustments
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      switch (filterName) {
        case 'Vintage':
          _brightness = 0.1;
          _contrast = 0.2;
          _saturation = -0.2;
          _warmth = 0.3;
          break;
        case 'B&W':
          _saturation = -1.0;
          _contrast = 0.2;
          break;
        case 'Dramatic':
          _contrast = 0.5;
          _saturation = 0.3;
          _brightness = -0.1;
          break;
        case 'Cool':
          _warmth = -0.3;
          _tint = -0.2;
          break;
        case 'Warm':
          _warmth = 0.4;
          _brightness = 0.1;
          break;
        case 'Soft':
          _brightness = 0.2;
          _contrast = -0.2;
          break;
        case 'Vivid':
          _saturation = 0.4;
          _contrast = 0.3;
          break;
        default:
          // Reset to original
          _brightness = 0.0;
          _contrast = 0.0;
          _saturation = 0.0;
          _warmth = 0.0;
          _tint = 0.0;
      }
      _isProcessing = false;
    });

    _loadingController.stop();
  }

  void _updateAdjustments(Map<String, double> adjustments) {
    setState(() {
      _brightness = adjustments['brightness'] ?? _brightness;
      _contrast = adjustments['contrast'] ?? _contrast;
      _saturation = adjustments['saturation'] ?? _saturation;
      _highlights = adjustments['highlights'] ?? _highlights;
      _shadows = adjustments['shadows'] ?? _shadows;
      _warmth = adjustments['warmth'] ?? _warmth;
      _tint = adjustments['tint'] ?? _tint;
      _clarity = adjustments['clarity'] ?? _clarity;
      _vibrance = adjustments['vibrance'] ?? _vibrance;
      _exposure = adjustments['exposure'] ?? _exposure;
      _gamma = adjustments['gamma'] ?? _gamma;
      _hue = adjustments['hue'] ?? _hue;
    });
    // Provide haptic feedback for real-time adjustments
    HapticFeedback.selectionClick();
  }

  void _resetCrop() {
    setState(() {
      _rotation = 0.0;
      _flipHorizontal = false;
      _flipVertical = false;
      _cropRect = null;
    });
    HapticFeedback.mediumImpact();
  }

  ColorFilter _buildColorMatrix() {
    // Create color matrix for all adjustments
    List<double> matrix = [
      1 + _contrast, 0, 0, 0, _brightness * 255,
      0, 1 + _contrast, 0, 0, _brightness * 255,
      0, 0, 1 + _contrast, 0, _brightness * 255,
      0, 0, 0, 1, 0,
    ];

    // Apply saturation
    if (_saturation != 0.0) {
      final satMatrix = _getSaturationMatrix(1 + _saturation);
      matrix = _multiplyMatrices(matrix, satMatrix);
    }

    // Apply warmth (temperature adjustment)
    if (_warmth != 0.0) {
      final warmthMatrix = _getWarmthMatrix(_warmth);
      matrix = _multiplyMatrices(matrix, warmthMatrix);
    }

    return ColorFilter.matrix(matrix);
  }

  List<double> _getSaturationMatrix(double saturation) {
    final lumR = 0.3086;
    final lumG = 0.6094;
    final lumB = 0.0820;

    final sr = (1 - saturation) * lumR;
    final sg = (1 - saturation) * lumG;
    final sb = (1 - saturation) * lumB;

    return [
      sr + saturation, sg, sb, 0, 0,
      sr, sg + saturation, sb, 0, 0,
      sr, sg, sb + saturation, 0, 0,
      0, 0, 0, 1, 0,
    ];
  }

  List<double> _getWarmthMatrix(double warmth) {
    return [
      1 + warmth * 0.2, 0, 0, 0, 0,
      0, 1, 0, 0, 0,
      0, 0, 1 - warmth * 0.2, 0, 0,
      0, 0, 0, 1, 0,
    ];
  }

  List<double> _multiplyMatrices(List<double> a, List<double> b) {
    List<double> result = List.filled(20, 0);

    for (int i = 0; i < 4; i++) {
      for (int j = 0; j < 5; j++) {
        for (int k = 0; k < 4; k++) {
          result[i * 5 + j] += a[i * 5 + k] * b[k * 5 + j];
        }
        if (j == 4) {
          result[i * 5 + j] += a[i * 5 + j];
        }
      }
    }

    return result;
  }

  void _saveImage() async {
    setState(() {
      _isProcessing = true;
      _processingText = 'Saving image...';
    });

    _loadingController.repeat();

    // Simulate save processing
    await Future.delayed(const Duration(milliseconds: 2000));

    _loadingController.stop();

    HapticFeedback.mediumImpact();
    Navigator.pop(context);
  }
}

enum EditingMode {
  filters,
  adjust,
  crop,
  effects,
}

class CropGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.5)
      ..strokeWidth = 1;

    // Vertical lines
    canvas.drawLine(
      Offset(size.width / 3, 0),
      Offset(size.width / 3, size.height),
      paint,
    );
    canvas.drawLine(
      Offset(size.width * 2 / 3, 0),
      Offset(size.width * 2 / 3, size.height),
      paint,
    );

    // Horizontal lines
    canvas.drawLine(
      Offset(0, size.height / 3),
      Offset(size.width, size.height / 3),
      paint,
    );
    canvas.drawLine(
      Offset(0, size.height * 2 / 3),
      Offset(size.width, size.height * 2 / 3),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
