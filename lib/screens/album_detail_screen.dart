import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/album.dart';
import '../providers/gallery_provider.dart';
import '../widgets/media_grid.dart';
import '../utils/constants.dart';
import 'viewer_screen.dart';

class AlbumDetailScreen extends StatelessWidget {
  final Album album;

  const AlbumDetailScreen({
    super.key,
    required this.album,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(album.name),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showAlbumOptions(context);
            },
          ),
        ],
      ),
      body: Consumer<GalleryProvider>(
        builder: (context, galleryProvider, child) {
          final albumMedia = galleryProvider.getMediaByAlbum(album.id);
          
          if (albumMedia.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.photo_library_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No Photos in Album',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'This album is empty.',
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return MediaGrid(
            media: albumMedia,
            gridColumns: galleryProvider.gridColumns,
            selectedItems: galleryProvider.selectedItems,
            isSelectionMode: galleryProvider.isSelectionMode,
            hasMoreMedia: false, // Albums are fully loaded
            isLoadingMore: false,
            onItemTap: (mediaItem) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ViewerScreen(
                    mediaItems: albumMedia,
                    initialIndex: albumMedia.indexOf(mediaItem),
                  ),
                ),
              );
            },
            onItemLongPress: (mediaItem) {
              if (!galleryProvider.isSelectionMode) {
                galleryProvider.toggleSelectionMode();
              }
              galleryProvider.toggleItemSelection(mediaItem.id);
            },
            onItemSelected: (mediaItem) {
              galleryProvider.toggleItemSelection(mediaItem.id);
            },
          );
        },
      ),
    );
  }

  void _showAlbumOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('Rename Album'),
            onTap: () {
              Navigator.pop(context);
              _renameAlbum(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete),
            title: const Text('Delete Album'),
            onTap: () {
              Navigator.pop(context);
              _deleteAlbum(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share Album'),
            onTap: () {
              Navigator.pop(context);
              _shareAlbum(context);
            },
          ),
        ],
      ),
    );
  }

  void _renameAlbum(BuildContext context) {
    final textController = TextEditingController(text: album.name);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename Album'),
        content: TextField(
          controller: textController,
          decoration: const InputDecoration(
            labelText: 'Album Name',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement album rename
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Album renamed successfully')),
              );
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  void _deleteAlbum(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Album'),
        content: Text('Are you sure you want to delete "${album.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement album deletion
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Album deleted successfully')),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _shareAlbum(BuildContext context) {
    // TODO: Implement album sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon')),
    );
  }
} 