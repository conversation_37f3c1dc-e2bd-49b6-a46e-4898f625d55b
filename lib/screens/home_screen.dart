import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shimmer/shimmer.dart';

import '../providers/gallery_provider.dart';
import '../providers/security_provider.dart';
import '../providers/theme_provider.dart';
import '../utils/constants.dart';
import '../widgets/perfect_media_grid.dart';
import '../widgets/modern_bottom_nav.dart';
import '../widgets/ad_banner.dart';
import 'albums_screen.dart';
import 'auth_screen.dart';
import 'favorites_screen.dart';
import 'hidden_screen.dart';
import 'settings_screen.dart';
import 'viewer_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  late BannerAd _bannerAd;
  bool _isBannerAdLoaded = false;

  final List<Widget> _screens = [
    const ModernMediaGridScreen(),
    const AlbumsScreen(),
    const FavoritesScreen(),
    const HiddenScreen(),
    const SettingsScreen(),
  ];

  final List<ModernNavItem> _navItems = [
    const ModernNavItem(
      icon: Icons.photo_library_outlined,
      activeIcon: Icons.photo_library_rounded,
      label: 'Gallery',
    ),
    const ModernNavItem(
      icon: Icons.photo_album_outlined,
      activeIcon: Icons.photo_album_rounded,
      label: 'Albums',
    ),
    const ModernNavItem(
      icon: Icons.favorite_border_rounded,
      activeIcon: Icons.favorite_rounded,
      label: 'Favorites',
    ),
    const ModernNavItem(
      icon: Icons.visibility_off_outlined,
      activeIcon: Icons.visibility_off_rounded,
      label: 'Hidden',
    ),
    const ModernNavItem(
      icon: Icons.settings_outlined,
      activeIcon: Icons.settings_rounded,
      label: 'Settings',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  Future<void> _loadBannerAd() async {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    
    // Only show ads if not premium
    if (!themeProvider.isPremium) {
      _bannerAd = BannerAd(
        adUnitId: AppConstants.bannerAdUnitId,
        size: AdSize.banner,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            setState(() {
              _isBannerAdLoaded = true;
            });
          },
          onAdFailedToLoad: (ad, error) {
            ad.dispose();
          },
        ),
      );
      
      await _bannerAd.load();
    }
  }

  @override
  void dispose() {
    if (_isBannerAdLoaded) {
      _bannerAd.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SecurityProvider>(
      builder: (context, securityProvider, child) {
        // Check if app is locked
        if (securityProvider.hasSecurity && securityProvider.isLocked) {
          return const AuthScreen();
        }

        return Scaffold(
          appBar: _buildAppBar(),
          body: Column(
            children: [
              Expanded(
                child: _screens[_currentIndex],
              ),
              // Show banner ad if not premium
              if (!Provider.of<ThemeProvider>(context).isPremium && _isBannerAdLoaded)
                AdBannerWidget(ad: _bannerAd),
            ],
          ),
          bottomNavigationBar: ModernBottomNav(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            items: _navItems,
          ),
          extendBody: true,
          floatingActionButton: _buildFloatingActionButton(),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(_getTitle()),
      actions: _buildAppBarActions(),
      elevation: 0,
    );
  }

  String _getTitle() {
    switch (_currentIndex) {
      case 0:
        return 'Gallery';
      case 1:
        return 'Albums';
      case 2:
        return 'Favorites';
      case 3:
        return 'Hidden';
      case 4:
        return 'Settings';
      default:
        return 'Gallery';
    }
  }

  List<Widget> _buildAppBarActions() {
    final galleryProvider = Provider.of<GalleryProvider>(context);
    
    switch (_currentIndex) {
      case 0: // Gallery
        return [
          if (galleryProvider.isSelectionMode) ...[
            IconButton(
              icon: const Icon(Icons.select_all),
              onPressed: () {
                if (galleryProvider.selectedCount == galleryProvider.filteredMedia.length) {
                  galleryProvider.deselectAll();
                } else {
                  galleryProvider.selectAll();
                }
              },
            ),
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                galleryProvider.toggleSelectionMode();
              },
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                // TODO: Implement search
              },
            ),
            PopupMenuButton<SortOrder>(
              icon: const Icon(Icons.sort),
              onSelected: (SortOrder order) {
                galleryProvider.setSortOrder(order);
              },
              itemBuilder: (BuildContext context) => [
                const PopupMenuItem(
                  value: SortOrder.date,
                  child: Text('Date'),
                ),
                const PopupMenuItem(
                  value: SortOrder.name,
                  child: Text('Name'),
                ),
                const PopupMenuItem(
                  value: SortOrder.size,
                  child: Text('Size'),
                ),
                const PopupMenuItem(
                  value: SortOrder.type,
                  child: Text('Type'),
                ),
              ],
            ),
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () {
                _showMoreOptions();
              },
            ),
          ],
        ];
      case 1: // Albums
        return [
          IconButton(
            icon: const Icon(Icons.create_new_folder),
            onPressed: () {
              // TODO: Create new album
            },
          ),
        ];
      case 2: // Favorites
        return [
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: () {
              // TODO: Sort favorites
            },
          ),
        ];
      case 3: // Hidden
        return [
          IconButton(
            icon: const Icon(Icons.visibility_off),
            onPressed: () {
              // TODO: Hide/unhide options
            },
          ),
        ];
      case 4: // Settings
        return [];
      default:
        return [];
    }
  }



  Widget? _buildFloatingActionButton() {
    final galleryProvider = Provider.of<GalleryProvider>(context);
    
    if (galleryProvider.isSelectionMode) {
      return FloatingActionButton.extended(
        onPressed: () {
          _showSelectionActions();
        },
        icon: const Icon(Icons.more_vert),
        label: Text('${galleryProvider.selectedCount} selected'),
      );
    }

    switch (_currentIndex) {
      case 0: // Gallery
        return FloatingActionButton(
          onPressed: () {
            // TODO: Take photo or import
          },
          child: const Icon(Icons.camera_alt),
        );
      case 1: // Albums
        return FloatingActionButton(
          onPressed: () {
            // TODO: Create new album
          },
          child: const Icon(Icons.create_new_folder),
        );
      default:
        return null;
    }
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.select_all),
              title: const Text('Select All'),
              onTap: () {
                Navigator.pop(context);
                final galleryProvider = Provider.of<GalleryProvider>(context, listen: false);
                galleryProvider.toggleSelectionMode();
              },
            ),
            ListTile(
              leading: const Icon(Icons.sort),
              title: const Text('Sort'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Show sort options
              },
            ),
            ListTile(
              leading: const Icon(Icons.filter_list),
              title: const Text('Filter'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Show filter options
              },
            ),
            ListTile(
              leading: const Icon(Icons.view_module),
              title: const Text('Change View'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Change view type
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showSelectionActions() {
    final galleryProvider = Provider.of<GalleryProvider>(context, listen: false);
    final selectedItems = galleryProvider.selectedItems.toList();
    
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.favorite),
            title: const Text('Add to Favorites'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Add to favorites
            },
          ),
          ListTile(
            leading: const Icon(Icons.visibility_off),
            title: const Text('Hide'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Hide items
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Share items
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete),
            title: const Text('Delete'),
            onTap: () {
              Navigator.pop(context);
              _showDeleteConfirmation(selectedItems);
            },
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(List<String> itemIds) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Items'),
        content: Text('Are you sure you want to delete ${itemIds.length} items?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final galleryProvider = Provider.of<GalleryProvider>(context, listen: false);
              galleryProvider.deleteItems(itemIds);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class ModernMediaGridScreen extends StatelessWidget {
  const ModernMediaGridScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GalleryProvider>(
      builder: (context, galleryProvider, child) {
        if (galleryProvider.isLoading) {
          return _buildShimmerGrid(galleryProvider.gridColumns);
        }

        if (!galleryProvider.hasPermission) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.lock,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Permission Required',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Please grant storage permission to access your photos and videos.',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    galleryProvider.requestPermissionsAndLoad();
                  },
                  child: const Text('Grant Permission'),
                ),
              ],
            ),
          );
        }

        if (galleryProvider.filteredMedia.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.photo_library_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                const Text(
                  'No Photos Found',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Take some photos or import them from your device.',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    galleryProvider.refresh();
                  },
                  child: const Text('Refresh'),
                ),
              ],
            ),
          );
        }

        return ModernMediaGrid(
          media: galleryProvider.filteredMedia,
          selectedItems: galleryProvider.selectedItems,
          isSelectionMode: galleryProvider.isSelectionMode,
          hasMoreMedia: galleryProvider.hasMoreMedia,
          isLoadingMore: galleryProvider.isLoadingMore,
          onLoadMore: () => galleryProvider.loadMoreMedia(),
          onItemTap: (mediaItem) {
            Navigator.push(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    ViewerScreen(
                  mediaItems: galleryProvider.filteredMedia,
                  initialIndex: galleryProvider.filteredMedia.indexOf(mediaItem),
                ),
                transitionsBuilder: (context, animation, secondaryAnimation, child) {
                  return FadeTransition(
                    opacity: animation,
                    child: child,
                  );
                },
                transitionDuration: const Duration(milliseconds: 300),
              ),
            );
          },
          onItemLongPress: (mediaItem) {
            HapticFeedback.mediumImpact();
            if (!galleryProvider.isSelectionMode) {
              galleryProvider.toggleSelectionMode();
            }
            galleryProvider.toggleItemSelection(mediaItem.id);
          },
          onItemSelected: (mediaItem) {
            galleryProvider.toggleItemSelection(mediaItem.id);
          },
        );
      },
    );
  }

  Widget _buildShimmerGrid(int gridColumns) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultGridSpacing),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: gridColumns,
        crossAxisSpacing: AppConstants.defaultGridSpacing,
        mainAxisSpacing: AppConstants.defaultGridSpacing,
        childAspectRatio: AppConstants.defaultGridChildAspectRatio,
      ),
      itemCount: 20, // Show 20 shimmer placeholders
      itemBuilder: (context, index) {
        return _buildShimmerItem();
      },
    );
  }

  Widget _buildShimmerItem() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}