import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui';
import '../design_system/nexus_theme.dart';
import '../widgets/dimensional_background.dart';

/// Immersive Photo Viewer - Revolutionary 3D photo experience
/// Features ambient backgrounds, gesture layers, and multi-photo blending
class ImmersiveViewer extends StatefulWidget {
  final List<String> imagePaths;
  final int initialIndex;

  const ImmersiveViewer({
    super.key,
    required this.imagePaths,
    required this.initialIndex,
  });

  @override
  State<ImmersiveViewer> createState() => _ImmersiveViewerState();
}

class _ImmersiveViewerState extends State<ImmersiveViewer>
    with TickerProviderStateMixin {
  late AnimationController _ambientController;
  late AnimationController _gestureController;
  late AnimationController _transitionController;
  
  late Animation<double> _ambientFlow;
  late Animation<double> _gestureResponse;
  late Animation<double> _transitionProgress;
  
  late PageController _pageController;
  int _currentIndex = 0;
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  bool _isZoomed = false;
  bool _isGestureMode = false;
  ViewMode _viewMode = ViewMode.single;
  
  // Ambient background colors extracted from current image
  List<Color> _ambientColors = [
    NexusTheme.cosmicPurple,
    NexusTheme.electricBlue,
  ];

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _ambientController = AnimationController(
      duration: NexusAnimations.infinite,
      vsync: this,
    );
    _ambientFlow = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _ambientController, curve: Curves.linear),
    );
    _ambientController.repeat();

    _gestureController = AnimationController(
      duration: NexusAnimations.organic,
      vsync: this,
    );
    _gestureResponse = CurvedAnimation(
      parent: _gestureController,
      curve: NexusTheme.organicCurve,
    );

    _transitionController = AnimationController(
      duration: NexusAnimations.cosmic,
      vsync: this,
    );
    _transitionProgress = CurvedAnimation(
      parent: _transitionController,
      curve: NexusTheme.warpCurve,
    );
  }

  @override
  void dispose() {
    _ambientController.dispose();
    _gestureController.dispose();
    _transitionController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _onImageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
    _extractAmbientColors();
  }

  void _extractAmbientColors() {
    // In a real implementation, this would analyze the image
    // and extract dominant colors for ambient background
    final random = math.Random(_currentIndex);
    _ambientColors = [
      Color.fromRGBO(
        random.nextInt(255),
        random.nextInt(255),
        random.nextInt(255),
        1.0,
      ),
      Color.fromRGBO(
        random.nextInt(255),
        random.nextInt(255),
        random.nextInt(255),
        1.0,
      ),
    ];
  }

  void _toggleViewMode() {
    setState(() {
      _viewMode = _viewMode == ViewMode.single 
          ? ViewMode.blend 
          : ViewMode.single;
    });
    _transitionController.forward().then((_) {
      _transitionController.reverse();
    });
  }

  void _toggleGestureMode() {
    setState(() {
      _isGestureMode = !_isGestureMode;
    });
    
    if (_isGestureMode) {
      _gestureController.forward();
    } else {
      _gestureController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NexusTheme.voidBlack,
      body: Stack(
        children: [
          // Ambient Background
          AnimatedBuilder(
            animation: _ambientController,
            builder: (context, child) {
              return _buildAmbientBackground();
            },
          ),

          // Main Photo Viewer
          _buildPhotoViewer(),

          // Gesture Layer Overlay
          if (_isGestureMode) _buildGestureLayer(),

          // Control Interface
          _buildControlInterface(),

          // Info Panel
          _buildInfoPanel(),
        ],
      ),
    );
  }

  Widget _buildAmbientBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 1.5,
          colors: [
            _ambientColors.first.withOpacity(0.3),
            _ambientColors.last.withOpacity(0.1),
            NexusTheme.voidBlack,
          ],
          stops: const [0.0, 0.7, 1.0],
          transform: GradientRotation(_ambientFlow.value),
        ),
      ),
      child: DimensionalBackground(
        rotation: _ambientFlow.value,
        intensity: 0.5,
      ),
    );
  }

  Widget _buildPhotoViewer() {
    return Center(
      child: AnimatedBuilder(
        animation: _transitionController,
        builder: (context, child) {
          if (_viewMode == ViewMode.blend) {
            return _buildBlendedView();
          } else {
            return _buildSingleView();
          }
        },
      ),
    );
  }

  Widget _buildSingleView() {
    return GestureDetector(
      onScaleStart: (details) {
        setState(() {
          _isZoomed = true;
        });
      },
      onScaleUpdate: (details) {
        setState(() {
          _scale = details.scale.clamp(0.5, 3.0);
          _offset = details.focalPoint - 
                   Offset(MediaQuery.of(context).size.width / 2,
                         MediaQuery.of(context).size.height / 2);
        });
      },
      onScaleEnd: (details) {
        if (_scale < 1.0) {
          setState(() {
            _scale = 1.0;
            _offset = Offset.zero;
            _isZoomed = false;
          });
        }
      },
      child: Transform.scale(
        scale: _scale,
        child: Transform.translate(
          offset: _offset,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
              boxShadow: NexusTheme.dimensionalShadow,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onImageChanged,
                itemCount: widget.imagePaths.length,
                itemBuilder: (context, index) {
                  return _buildImageContainer(widget.imagePaths[index]);
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBlendedView() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Background image
        if (_currentIndex > 0)
          Positioned(
            child: Opacity(
              opacity: 0.3,
              child: Transform.scale(
                scale: 1.2,
                child: _buildImageContainer(widget.imagePaths[_currentIndex - 1]),
              ),
            ),
          ),
        
        // Main image
        _buildImageContainer(widget.imagePaths[_currentIndex]),
        
        // Foreground image
        if (_currentIndex < widget.imagePaths.length - 1)
          Positioned(
            child: Opacity(
              opacity: 0.3,
              child: Transform.scale(
                scale: 0.8,
                child: _buildImageContainer(widget.imagePaths[_currentIndex + 1]),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildImageContainer(String imagePath) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.1),
            Colors.transparent,
            Colors.black.withOpacity(0.1),
          ],
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
          child: Container(
            color: NexusTheme.deepSpace.withOpacity(0.3),
            child: Center(
              child: Icon(
                Icons.image,
                size: 100,
                color: NexusTheme.crystalWhite.withOpacity(0.5),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGestureLayer() {
    return AnimatedBuilder(
      animation: _gestureController,
      builder: (context, child) {
        return Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [
                  Colors.transparent,
                  NexusTheme.electricBlue.withOpacity(0.1 * _gestureResponse.value),
                ],
              ),
            ),
            child: CustomPaint(
              painter: GestureLayerPainter(
                intensity: _gestureResponse.value,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlInterface() {
    return Positioned(
      bottom: MediaQuery.of(context).padding.bottom + NexusTheme.cellularSpace,
      left: NexusTheme.cellularSpace,
      right: NexusTheme.cellularSpace,
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          gradient: NexusTheme.memoryBubble,
          borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
          boxShadow: NexusTheme.dimensionalShadow,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildControlButton(
                  icon: Icons.view_in_ar,
                  label: 'Blend',
                  isActive: _viewMode == ViewMode.blend,
                  onTap: _toggleViewMode,
                ),
                _buildControlButton(
                  icon: Icons.gesture,
                  label: 'Gesture',
                  isActive: _isGestureMode,
                  onTap: _toggleGestureMode,
                ),
                _buildControlButton(
                  icon: Icons.palette,
                  label: 'Ambient',
                  onTap: () => _extractAmbientColors(),
                ),
                _buildControlButton(
                  icon: Icons.share,
                  label: 'Share',
                  onTap: () {},
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    bool isActive = false,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: isActive 
              ? NexusTheme.electricBlue.withOpacity(0.3)
              : Colors.transparent,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isActive 
                  ? NexusTheme.neonGreen 
                  : NexusTheme.crystalWhite,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: NexusTheme.whisperText.copyWith(
                color: isActive 
                    ? NexusTheme.neonGreen 
                    : NexusTheme.crystalWhite,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoPanel() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + NexusTheme.cellularSpace,
      left: NexusTheme.cellularSpace,
      right: NexusTheme.cellularSpace,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: NexusTheme.memoryBubble,
          borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
          boxShadow: NexusTheme.dimensionalShadow,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Icon(
                    Icons.arrow_back,
                    color: NexusTheme.crystalWhite,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Photo ${_currentIndex + 1} of ${widget.imagePaths.length}',
                        style: NexusTheme.memoryLabel.copyWith(
                          color: NexusTheme.crystalWhite,
                        ),
                      ),
                      Text(
                        'Immersive View Mode',
                        style: NexusTheme.whisperText,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.more_vert,
                  color: NexusTheme.crystalWhite,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Custom painter for gesture layer
class GestureLayerPainter extends CustomPainter {
  final double intensity;

  GestureLayerPainter({required this.intensity});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = NexusTheme.electricBlue.withOpacity(0.2 * intensity)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // Draw gesture indicators
    final center = Offset(size.width / 2, size.height / 2);
    canvas.drawCircle(center, 50 * intensity, paint);
    canvas.drawCircle(center, 100 * intensity, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

enum ViewMode { single, blend }
