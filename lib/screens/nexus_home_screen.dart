import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui';
import '../design_system/nexus_theme.dart';
import '../widgets/memory_bubble.dart';
import '../widgets/dimensional_background.dart';
import '../widgets/quantum_search.dart';

/// NEXUS Home Screen - The Living Memory Ecosystem
/// Features floating 3D memory bubbles in a cosmic environment
class NexusHomeScreen extends StatefulWidget {
  const NexusHomeScreen({super.key});

  @override
  State<NexusHomeScreen> createState() => _NexusHomeScreenState();
}

class _NexusHomeScreenState extends State<NexusHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _cosmicController;
  late AnimationController _bubbleController;
  late AnimationController _pulseController;
  
  late Animation<double> _cosmicRotation;
  late Animation<double> _bubbleFloat;
  late Animation<double> _pulseGlow;
  
  bool _isSearchActive = false;
  bool _isDimensionShifting = false;
  
  // Memory bubble data - AI-generated clusters
  final List<MemoryCluster> _memoryClusters = [
    MemoryCluster(
      id: 'recent_adventures',
      title: 'Recent Adventures',
      emotion: EmotionType.joy,
      itemCount: 47,
      dominantColors: [NexusTheme.neonGreen, NexusTheme.aquaMist],
      position: const Offset(0.2, 0.3),
      size: 120.0,
      energy: 0.8,
    ),
    MemoryCluster(
      id: 'golden_moments',
      title: 'Golden Moments',
      emotion: EmotionType.love,
      itemCount: 23,
      dominantColors: [NexusTheme.goldenAura, NexusTheme.sunsetOrange],
      position: const Offset(0.7, 0.2),
      size: 95.0,
      energy: 0.9,
    ),
    MemoryCluster(
      id: 'midnight_memories',
      title: 'Midnight Memories',
      emotion: EmotionType.mystery,
      itemCount: 31,
      dominantColors: [NexusTheme.cosmicPurple, NexusTheme.electricBlue],
      position: const Offset(0.1, 0.7),
      size: 110.0,
      energy: 0.6,
    ),
    MemoryCluster(
      id: 'summer_vibes',
      title: 'Summer Vibes',
      emotion: EmotionType.energy,
      itemCount: 89,
      dominantColors: [NexusTheme.magentaFlare, NexusTheme.crimsonPulse],
      position: const Offset(0.8, 0.6),
      size: 140.0,
      energy: 1.0,
    ),
    MemoryCluster(
      id: 'quiet_reflections',
      title: 'Quiet Reflections',
      emotion: EmotionType.peace,
      itemCount: 15,
      dominantColors: [NexusTheme.aquaMist, NexusTheme.silverMist],
      position: const Offset(0.5, 0.5),
      size: 80.0,
      energy: 0.4,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Cosmic background rotation
    _cosmicController = AnimationController(
      duration: NexusAnimations.infinite,
      vsync: this,
    );
    _cosmicRotation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _cosmicController, curve: Curves.linear),
    );
    _cosmicController.repeat();

    // Memory bubble floating
    _bubbleController = AnimationController(
      duration: NexusAnimations.cosmic,
      vsync: this,
    );
    _bubbleFloat = NexusAnimations.floatingMotion(_bubbleController);
    _bubbleController.repeat(reverse: true);

    // Pulse glow for interactions
    _pulseController = AnimationController(
      duration: NexusAnimations.organic,
      vsync: this,
    );
    _pulseGlow = NexusAnimations.pulseGlow(_pulseController);
  }

  @override
  void dispose() {
    _cosmicController.dispose();
    _bubbleController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _onMemoryClusterTap(MemoryCluster cluster) {
    setState(() {
      _isDimensionShifting = true;
    });
    
    _pulseController.forward().then((_) {
      // Navigate to dimensional memory view
      Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              DimensionalMemoryView(cluster: cluster),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 1.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: NexusTheme.warpCurve,
              )),
              child: child,
            );
          },
          transitionDuration: NexusAnimations.cosmic,
        ),
      ).then((_) {
        setState(() {
          _isDimensionShifting = false;
        });
        _pulseController.reverse();
      });
    });
  }

  void _toggleQuantumSearch() {
    setState(() {
      _isSearchActive = !_isSearchActive;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NexusTheme.voidBlack,
      body: Stack(
        children: [
          // Dimensional Background with Particles
          AnimatedBuilder(
            animation: _cosmicController,
            builder: (context, child) {
              return DimensionalBackground(
                rotation: _cosmicRotation.value,
                intensity: _isDimensionShifting ? 1.5 : 1.0,
              );
            },
          ),

          // Memory Bubble Ecosystem
          AnimatedBuilder(
            animation: Listenable.merge([_bubbleController, _pulseController]),
            builder: (context, child) {
              return CustomPaint(
                painter: MemoryEcosystemPainter(
                  clusters: _memoryClusters,
                  floatOffset: _bubbleFloat.value,
                  pulseIntensity: _pulseGlow.value,
                  isDimensionShifting: _isDimensionShifting,
                ),
                size: Size.infinite,
              );
            },
          ),

          // Interactive Memory Bubbles
          ...(_memoryClusters.map((cluster) => _buildMemoryBubble(cluster))),

          // Quantum Search Interface
          Positioned(
            top: MediaQuery.of(context).padding.top + NexusTheme.cellularSpace,
            left: NexusTheme.cellularSpace,
            right: NexusTheme.cellularSpace,
            child: QuantumSearch(
              isActive: _isSearchActive,
              onToggle: _toggleQuantumSearch,
              onSearch: (query) {
                // Implement AI-powered contextual search
                _performQuantumSearch(query);
              },
            ),
          ),

          // Dimensional Controls
          Positioned(
            bottom: MediaQuery.of(context).padding.bottom + NexusTheme.cellularSpace,
            left: NexusTheme.cellularSpace,
            right: NexusTheme.cellularSpace,
            child: _buildDimensionalControls(),
          ),

          // Neural Network Overlay (when searching)
          if (_isSearchActive)
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  color: NexusTheme.voidBlack.withOpacity(0.3),
                  child: const NeuralSearchOverlay(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMemoryBubble(MemoryCluster cluster) {
    final screenSize = MediaQuery.of(context).size;
    final position = Offset(
      cluster.position.dx * screenSize.width,
      cluster.position.dy * screenSize.height,
    );

    return AnimatedBuilder(
      animation: _bubbleController,
      builder: (context, child) {
        return Positioned(
          left: position.dx - cluster.size / 2,
          top: position.dy - cluster.size / 2 + _bubbleFloat.value.dy,
          child: GestureDetector(
            onTap: () => _onMemoryClusterTap(cluster),
            child: MemoryBubble(
              cluster: cluster,
              isActive: _isDimensionShifting,
              pulseIntensity: _pulseGlow.value,
            ),
          ),
        );
      },
    );
  }

  Widget _buildDimensionalControls() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        gradient: NexusTheme.memoryBubble,
        borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
        boxShadow: NexusTheme.dimensionalShadow,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                icon: NexusIcons.timeSpiral,
                label: 'Timeline',
                onTap: () => _navigateToTimeline(),
              ),
              _buildControlButton(
                icon: NexusIcons.emotionCloud,
                label: 'Emotions',
                onTap: () => _navigateToEmotions(),
              ),
              _buildControlButton(
                icon: NexusIcons.storyArc,
                label: 'Stories',
                onTap: () => _navigateToStories(),
              ),
              _buildControlButton(
                icon: NexusIcons.dimensionalView,
                label: 'Explore',
                onTap: () => _navigateToExplore(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: NexusTheme.crystalWhite,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: NexusTheme.whisperText,
          ),
        ],
      ),
    );
  }

  void _performQuantumSearch(String query) {
    // Implement AI-powered search with natural language processing
    // Example: "Show me beach photos from last summer"
    // This would trigger neural network analysis and bubble reorganization
  }

  void _navigateToTimeline() {
    // Navigate to spiral timeline view
  }

  void _navigateToEmotions() {
    // Navigate to emotion-based clustering
  }

  void _navigateToStories() {
    // Navigate to AI-generated story arcs
  }

  void _navigateToExplore() {
    // Navigate to 3D exploration mode
  }
}

// Data Models
class MemoryCluster {
  final String id;
  final String title;
  final EmotionType emotion;
  final int itemCount;
  final List<Color> dominantColors;
  final Offset position;
  final double size;
  final double energy;

  const MemoryCluster({
    required this.id,
    required this.title,
    required this.emotion,
    required this.itemCount,
    required this.dominantColors,
    required this.position,
    required this.size,
    required this.energy,
  });
}

enum EmotionType { joy, love, mystery, energy, peace, nostalgia, adventure }

// Placeholder widgets - to be implemented
class DimensionalMemoryView extends StatelessWidget {
  final MemoryCluster cluster;
  const DimensionalMemoryView({super.key, required this.cluster});
  @override
  Widget build(BuildContext context) => Container();
}

class NeuralSearchOverlay extends StatelessWidget {
  const NeuralSearchOverlay({super.key});
  @override
  Widget build(BuildContext context) => Container();
}

class MemoryEcosystemPainter extends CustomPainter {
  final List<MemoryCluster> clusters;
  final Offset floatOffset;
  final double pulseIntensity;
  final bool isDimensionShifting;

  MemoryEcosystemPainter({
    required this.clusters,
    required this.floatOffset,
    required this.pulseIntensity,
    required this.isDimensionShifting,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Paint connecting energy lines between memory clusters
    final paint = Paint()
      ..color = NexusTheme.electricBlue.withOpacity(0.3)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < clusters.length; i++) {
      for (int j = i + 1; j < clusters.length; j++) {
        final start = Offset(
          clusters[i].position.dx * size.width,
          clusters[i].position.dy * size.height,
        );
        final end = Offset(
          clusters[j].position.dx * size.width,
          clusters[j].position.dy * size.height,
        );
        
        canvas.drawLine(start, end, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
