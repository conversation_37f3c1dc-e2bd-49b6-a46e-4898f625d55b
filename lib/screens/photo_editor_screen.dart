import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:image/image.dart' as img;
import 'package:photo_manager/photo_manager.dart';

import '../models/media_item.dart';
import '../providers/theme_provider.dart';

class PhotoEditorScreen extends StatefulWidget {
  final MediaItem mediaItem;

  const PhotoEditorScreen({
    super.key,
    required this.mediaItem,
  });

  @override
  State<PhotoEditorScreen> createState() => _PhotoEditorScreenState();
}

class _PhotoEditorScreenState extends State<PhotoEditorScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  
  Uint8List? _originalImageData;
  Uint8List? _editedImageData;
  bool _isLoading = true;
  
  // Editing parameters
  double _brightness = 0.0;
  double _contrast = 1.0;
  double _saturation = 1.0;
  double _hue = 0.0;
  double _blur = 0.0;
  double _rotation = 0.0;
  
  // Current editing mode
  EditingMode _currentMode = EditingMode.adjust;
  
  // Filter presets
  final List<FilterPreset> _filterPresets = [
    FilterPreset('Original', brightness: 0, contrast: 1, saturation: 1),
    FilterPreset('Vintage', brightness: 0.1, contrast: 1.2, saturation: 0.8),
    FilterPreset('Dramatic', brightness: -0.1, contrast: 1.5, saturation: 1.3),
    FilterPreset('Cool', brightness: 0.05, contrast: 1.1, saturation: 0.9),
    FilterPreset('Warm', brightness: 0.1, contrast: 1.1, saturation: 1.2),
    FilterPreset('B&W', brightness: 0, contrast: 1.2, saturation: 0),
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadImage();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _slideController.forward();
  }

  Future<void> _loadImage() async {
    try {
      if (widget.mediaItem.assetEntity != null) {
        final imageData = await widget.mediaItem.assetEntity!.originBytes;
        if (mounted) {
          setState(() {
            _originalImageData = imageData;
            _editedImageData = imageData;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: Colors.black,
      body: SlideTransition(
        position: _slideAnimation,
        child: Column(
          children: [
            _buildTopBar(isDark),
            Expanded(
              child: _buildImageEditor(isDark),
            ),
            _buildBottomControls(isDark),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar(bool isDark) {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            _buildTopButton(
              icon: Icons.close_rounded,
              onTap: () => Navigator.pop(context),
            ),
            const Spacer(),
            Text(
              'Photo Editor',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            _buildTopButton(
              icon: Icons.check_rounded,
              onTap: _saveImage,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(22),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildImageEditor(bool isDark) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }

    if (_editedImageData == null) {
      return const Center(
        child: Icon(
          Icons.broken_image_rounded,
          color: Colors.white,
          size: 64,
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Transform.rotate(
          angle: _rotation * (3.14159 / 180),
          child: ColorFiltered(
            colorFilter: ColorFilter.matrix(_buildColorMatrix()),
            child: Image.memory(
              _editedImageData!,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControls(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildModeSelector(),
            const SizedBox(height: 16),
            _buildEditingControls(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildModeSelector() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: EditingMode.values.map((mode) {
          final isSelected = _currentMode == mode;
          return GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              setState(() {
                _currentMode = mode;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected 
                  ? AppColors.lightPrimary
                  : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected 
                    ? AppColors.lightPrimary
                    : Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Text(
                _getModeTitle(mode),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEditingControls() {
    switch (_currentMode) {
      case EditingMode.adjust:
        return _buildAdjustControls();
      case EditingMode.filters:
        return _buildFilterControls();
      case EditingMode.crop:
        return _buildCropControls();
      case EditingMode.effects:
        return _buildEffectsControls();
    }
  }

  Widget _buildAdjustControls() {
    return Container(
      height: 120,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildSliderControl('Brightness', _brightness, -1.0, 1.0, (value) {
            setState(() => _brightness = value);
            _applyEdits();
          }),
          _buildSliderControl('Contrast', _contrast, 0.0, 2.0, (value) {
            setState(() => _contrast = value);
            _applyEdits();
          }),
          _buildSliderControl('Saturation', _saturation, 0.0, 2.0, (value) {
            setState(() => _saturation = value);
            _applyEdits();
          }),
          _buildSliderControl('Hue', _hue, -180.0, 180.0, (value) {
            setState(() => _hue = value);
            _applyEdits();
          }),
        ],
      ),
    );
  }

  Widget _buildFilterControls() {
    return Container(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _filterPresets.length,
        itemBuilder: (context, index) {
          final preset = _filterPresets[index];
          return GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              _applyFilterPreset(preset);
            },
            child: Container(
              width: 80,
              margin: const EdgeInsets.only(right: 12),
              child: Column(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(11),
                      child: _originalImageData != null
                        ? Image.memory(
                            _originalImageData!,
                            fit: BoxFit.cover,
                          )
                        : Container(color: Colors.grey),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    preset.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCropControls() {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Text(
            'Rotation',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          Slider(
            value: _rotation,
            min: -45.0,
            max: 45.0,
            divisions: 90,
            activeColor: AppColors.lightPrimary,
            inactiveColor: Colors.white.withOpacity(0.3),
            onChanged: (value) {
              setState(() => _rotation = value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEffectsControls() {
    return Container(
      height: 120,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildSliderControl('Blur', _blur, 0.0, 10.0, (value) {
            setState(() => _blur = value);
            _applyEdits();
          }),
        ],
      ),
    );
  }

  Widget _buildSliderControl(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 16),
      child: Column(
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: Slider(
                value: value,
                min: min,
                max: max,
                activeColor: AppColors.lightPrimary,
                inactiveColor: Colors.white.withOpacity(0.3),
                onChanged: onChanged,
              ),
            ),
          ),
          Text(
            value.toStringAsFixed(1),
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  List<double> _buildColorMatrix() {
    // Create color matrix for brightness, contrast, saturation adjustments
    final matrix = List<double>.filled(20, 0);
    
    // Identity matrix
    matrix[0] = _contrast * _saturation;  // Red
    matrix[6] = _contrast * _saturation;  // Green  
    matrix[12] = _contrast * _saturation; // Blue
    matrix[18] = 1; // Alpha
    
    // Brightness
    matrix[4] = _brightness * 255;  // Red offset
    matrix[9] = _brightness * 255;  // Green offset
    matrix[14] = _brightness * 255; // Blue offset
    
    return matrix;
  }

  void _applyEdits() {
    // This is a simplified version - in a real app you'd use image processing libraries
    // For now, we'll just trigger a rebuild with the color matrix
    setState(() {});
  }

  void _applyFilterPreset(FilterPreset preset) {
    setState(() {
      _brightness = preset.brightness;
      _contrast = preset.contrast;
      _saturation = preset.saturation;
    });
    _applyEdits();
  }

  String _getModeTitle(EditingMode mode) {
    switch (mode) {
      case EditingMode.adjust:
        return 'Adjust';
      case EditingMode.filters:
        return 'Filters';
      case EditingMode.crop:
        return 'Crop';
      case EditingMode.effects:
        return 'Effects';
    }
  }

  void _saveImage() {
    // TODO: Implement image saving with applied edits
    HapticFeedback.mediumImpact();
    Navigator.pop(context);
  }
}

enum EditingMode {
  adjust,
  filters,
  crop,
  effects,
}

class FilterPreset {
  final String name;
  final double brightness;
  final double contrast;
  final double saturation;

  FilterPreset(
    this.name, {
    required this.brightness,
    required this.contrast,
    required this.saturation,
  });
}
