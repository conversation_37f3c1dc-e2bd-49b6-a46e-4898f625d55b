import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:io';
import 'package:image/image.dart' as img;
import 'package:photo_manager/photo_manager.dart';
import 'package:path_provider/path_provider.dart';

import '../models/media_item.dart';
import '../providers/theme_provider.dart';
import '../widgets/professional_filters.dart' as filters;

class PhotoEditorScreen extends StatefulWidget {
  final MediaItem mediaItem;

  const PhotoEditorScreen({
    super.key,
    required this.mediaItem,
  });

  @override
  State<PhotoEditorScreen> createState() => _PhotoEditorScreenState();
}

class _PhotoEditorScreenState extends State<PhotoEditorScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _toolbarController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _toolbarAnimation;

  Uint8List? _originalImageData;
  Uint8List? _editedImageData;
  Uint8List? _previewImageData;
  bool _isLoading = true;
  bool _isProcessing = false;

  // Editing parameters
  double _brightness = 0.0;
  double _contrast = 1.0;
  double _saturation = 1.0;
  double _hue = 0.0;
  double _warmth = 0.0;
  double _tint = 0.0;
  double _highlights = 0.0;
  double _shadows = 0.0;
  double _clarity = 0.0;
  double _vibrance = 0.0;
  double _blur = 0.0;
  double _sharpen = 0.0;
  double _rotation = 0.0;
  double _vignette = 0.0;
  double _grain = 0.0;

  // Current editing mode
  EditingMode _currentMode = EditingMode.filters;
  filters.FilterPreset? _selectedFilter;

  // Professional filters
  final List<filters.FilterPreset> _filterPresets = filters.ProfessionalFilters.allFilters;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadImage();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _toolbarController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _toolbarAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _toolbarController,
      curve: Curves.easeInOut,
    ));

    _slideController.forward();
    _toolbarController.forward();
  }

  Future<void> _loadImage() async {
    try {
      if (widget.mediaItem.assetEntity != null) {
        final imageData = await widget.mediaItem.assetEntity!.originBytes;
        if (mounted) {
          setState(() {
            _originalImageData = imageData;
            _editedImageData = imageData;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _toolbarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: Colors.black,
      body: SlideTransition(
        position: _slideAnimation,
        child: Stack(
          children: [
            Column(
              children: [
                _buildTopBar(isDark),
                Expanded(
                  child: _buildImageEditor(isDark),
                ),
                _buildBottomControls(isDark),
              ],
            ),
            if (_isProcessing) _buildProcessingOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar(bool isDark) {
    return SafeArea(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            _buildTopButton(
              icon: Icons.close_rounded,
              onTap: () => Navigator.pop(context),
            ),
            const Spacer(),
            Text(
              'Photo Editor',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            _buildTopButton(
              icon: Icons.check_rounded,
              onTap: _saveImage,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(22),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildImageEditor(bool isDark) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }

    if (_editedImageData == null) {
      return const Center(
        child: Icon(
          Icons.broken_image_rounded,
          color: Colors.white,
          size: 64,
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Transform.rotate(
          angle: _rotation * (3.14159 / 180),
          child: ColorFiltered(
            colorFilter: ColorFilter.matrix(_buildColorMatrix()),
            child: Image.memory(
              _editedImageData!,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControls(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildModeSelector(),
            const SizedBox(height: 16),
            _buildEditingControls(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildModeSelector() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: EditingMode.values.map((mode) {
          final isSelected = _currentMode == mode;
          return GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              setState(() {
                _currentMode = mode;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected 
                  ? AppColors.lightPrimary
                  : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected 
                    ? AppColors.lightPrimary
                    : Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Text(
                _getModeTitle(mode),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEditingControls() {
    switch (_currentMode) {
      case EditingMode.adjust:
        return _buildAdjustControls();
      case EditingMode.filters:
        return _buildFilterControls();
      case EditingMode.crop:
        return _buildCropControls();
      case EditingMode.effects:
        return _buildEffectsControls();
    }
  }

  Widget _buildAdjustControls() {
    return Container(
      height: 120,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildSliderControl('Brightness', _brightness, -1.0, 1.0, (value) {
            setState(() => _brightness = value);
            _applyEdits();
          }),
          _buildSliderControl('Contrast', _contrast, 0.0, 2.0, (value) {
            setState(() => _contrast = value);
            _applyEdits();
          }),
          _buildSliderControl('Saturation', _saturation, 0.0, 2.0, (value) {
            setState(() => _saturation = value);
            _applyEdits();
          }),
          _buildSliderControl('Hue', _hue, -180.0, 180.0, (value) {
            setState(() => _hue = value);
            _applyEdits();
          }),
        ],
      ),
    );
  }

  Widget _buildFilterControls() {
    return Container(
      height: 140,
      child: Column(
        children: [
          // Filter categories
          Container(
            height: 40,
            child: ListView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                _buildFilterCategory('All', true),
                _buildFilterCategory('Vintage', false),
                _buildFilterCategory('Dramatic', false),
                _buildFilterCategory('Nature', false),
                _buildFilterCategory('Portrait', false),
                _buildFilterCategory('Artistic', false),
                _buildFilterCategory('Cinema', false),
              ],
            ),
          ),
          const SizedBox(height: 8),
          // Filter grid
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _filterPresets.length,
              itemBuilder: (context, index) {
                final preset = _filterPresets[index];
                final isSelected = _selectedFilter?.name == preset.name;

                return GestureDetector(
                  onTap: () async {
                    HapticFeedback.lightImpact();
                    await _applyFilterPreset(preset);
                  },
                  child: Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 12),
                    child: Column(
                      children: [
                        Container(
                          width: 64,
                          height: 64,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: isSelected
                                ? AppColors.lightPrimary
                                : Colors.white.withOpacity(0.3),
                              width: isSelected ? 3 : 1,
                            ),
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: AppColors.lightPrimary.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ] : null,
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(isSelected ? 13 : 15),
                            child: Stack(
                              fit: StackFit.expand,
                              children: [
                                _originalImageData != null
                                  ? Image.memory(
                                      _originalImageData!,
                                      fit: BoxFit.cover,
                                    )
                                  : Container(color: Colors.grey.shade800),
                                Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Colors.transparent,
                                        Colors.black.withOpacity(0.6),
                                      ],
                                    ),
                                  ),
                                ),
                                Positioned(
                                  bottom: 4,
                                  left: 4,
                                  right: 4,
                                  child: Icon(
                                    preset.icon,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          preset.name,
                          style: TextStyle(
                            color: isSelected ? AppColors.lightPrimary : Colors.white,
                            fontSize: 11,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterCategory(String name, bool isSelected) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // TODO: Filter by category
      },
      child: Container(
        margin: const EdgeInsets.only(right: 12),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
            ? AppColors.lightPrimary
            : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
              ? AppColors.lightPrimary
              : Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Text(
          name,
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildCropControls() {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Text(
            'Rotation',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          Slider(
            value: _rotation,
            min: -45.0,
            max: 45.0,
            divisions: 90,
            activeColor: AppColors.lightPrimary,
            inactiveColor: Colors.white.withOpacity(0.3),
            onChanged: (value) {
              setState(() => _rotation = value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEffectsControls() {
    return Container(
      height: 120,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildSliderControl('Blur', _blur, 0.0, 10.0, (value) {
            setState(() => _blur = value);
            _applyEdits();
          }),
        ],
      ),
    );
  }

  Widget _buildSliderControl(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 16),
      child: Column(
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: Slider(
                value: value,
                min: min,
                max: max,
                activeColor: AppColors.lightPrimary,
                inactiveColor: Colors.white.withOpacity(0.3),
                onChanged: onChanged,
              ),
            ),
          ),
          Text(
            value.toStringAsFixed(1),
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  List<double> _buildColorMatrix() {
    // Create color matrix for brightness, contrast, saturation adjustments
    final matrix = List<double>.filled(20, 0);
    
    // Identity matrix
    matrix[0] = _contrast * _saturation;  // Red
    matrix[6] = _contrast * _saturation;  // Green  
    matrix[12] = _contrast * _saturation; // Blue
    matrix[18] = 1; // Alpha
    
    // Brightness
    matrix[4] = _brightness * 255;  // Red offset
    matrix[9] = _brightness * 255;  // Green offset
    matrix[14] = _brightness * 255; // Blue offset
    
    return matrix;
  }

  void _applyEdits() {
    // This is a simplified version - in a real app you'd use image processing libraries
    // For now, we'll just trigger a rebuild with the color matrix
    setState(() {});
  }

  Future<void> _applyFilterPreset(filters.FilterPreset preset) async {
    setState(() {
      _isProcessing = true;
      _selectedFilter = preset;
    });

    try {
      if (_originalImageData != null) {
        final filteredImage = await _processImageInBackground(_originalImageData!, preset);
        if (filteredImage != null && mounted) {
          setState(() {
            _editedImageData = filteredImage;
            _brightness = preset.brightness;
            _contrast = preset.contrast;
            _saturation = preset.saturation;
            _hue = preset.hue;
            _warmth = preset.warmth;
            _tint = preset.tint;
            _vignette = preset.vignette;
            _grain = preset.grain;
          });
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<Uint8List?> _processImageInBackground(Uint8List imageData, filters.FilterPreset filter) async {
    return await Future.delayed(const Duration(milliseconds: 100), () {
      return filters.ProfessionalFilters.applyFilter(imageData, filter);
    });
  }

  Widget _buildProcessingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                width: 32,
                height: 32,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.lightPrimary),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Applying Filter...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getModeTitle(EditingMode mode) {
    switch (mode) {
      case EditingMode.adjust:
        return 'Adjust';
      case EditingMode.filters:
        return 'Filters';
      case EditingMode.crop:
        return 'Crop';
      case EditingMode.effects:
        return 'Effects';
    }
  }

  void _saveImage() {
    // TODO: Implement image saving with applied edits
    HapticFeedback.mediumImpact();
    Navigator.pop(context);
  }
}

enum EditingMode {
  adjust,
  filters,
  crop,
  effects,
}
