import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:photo_manager/photo_manager.dart';

import '../models/media_item.dart';
import '../providers/theme_provider.dart';

class ProPhotoEditor extends StatefulWidget {
  final MediaItem mediaItem;

  const ProPhotoEditor({
    super.key,
    required this.mediaItem,
  });

  @override
  State<ProPhotoEditor> createState() => _ProPhotoEditorState();
}

class _ProPhotoEditorState extends State<ProPhotoEditor>
    with TickerProviderStateMixin {

  // Controllers
  late AnimationController _slideController;
  late AnimationController _shimmerController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _shimmerAnimation;

  // Image data
  Uint8List? _originalImageData;
  Uint8List? _editedImageData;
  bool _isLoading = true;
  bool _isProcessing = false;
  String _processingText = '';

  // Editing state
  EditorMode _currentMode = EditorMode.filters;
  String? _selectedFilter;

  // Filter values
  double _brightness = 0.0;
  double _contrast = 1.0;
  double _saturation = 1.0;
  double _warmth = 0.0;

  // Crop values
  double _rotation = 0.0;
  bool _flipHorizontal = false;
  bool _flipVertical = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadImage();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    _slideController.forward();
  }

  Future<void> _loadImage() async {
    try {
      if (widget.mediaItem.assetEntity != null) {
        final imageData = await widget.mediaItem.assetEntity!.originBytes;
        if (mounted && imageData != null) {
          setState(() {
            _originalImageData = imageData;
            _editedImageData = imageData;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SlideTransition(
        position: _slideAnimation,
        child: SafeArea(
          child: Column(
            children: [
              _buildTopBar(),
              Expanded(
                child: _buildImageArea(),
              ),
              _buildBottomControls(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_back_ios_rounded,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const Spacer(),
          const Text(
            'Photo Editor',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: _saveImage,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.check_rounded,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageArea() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }

    if (_editedImageData == null) {
      return const Center(
        child: Icon(
          Icons.broken_image_rounded,
          color: Colors.white,
          size: 64,
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(20),
      child: Center(
        child: AspectRatio(
          aspectRatio: 1.0,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.5),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  _buildProcessedImage(),
                  if (_isProcessing) _buildProcessingOverlay(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProcessedImage() {
    return Transform.rotate(
      angle: _rotation * (math.pi / 180),
      child: Transform.scale(
        scaleX: _flipHorizontal ? -1 : 1,
        scaleY: _flipVertical ? -1 : 1,
        child: ColorFiltered(
          colorFilter: _buildColorMatrix(),
          child: Image.memory(
            _editedImageData!,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  ColorFilter _buildColorMatrix() {
    final brightness = _brightness * 255;
    final contrast = _contrast;
    final saturation = _saturation;

    final lumR = 0.2126;
    final lumG = 0.7152;
    final lumB = 0.0722;
    final sr = (1 - saturation) * lumR;
    final sg = (1 - saturation) * lumG;
    final sb = (1 - saturation) * lumB;

    List<double> matrix = [
      sr + saturation, sg, sb, 0, brightness,
      sr, sg + saturation, sb, 0, brightness,
      sr, sg, sb + saturation, 0, brightness,
      0, 0, 0, 1, 0,
    ];

    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
        matrix[i * 5 + j] *= contrast;
      }
    }

    if (_warmth != 0.0) {
      matrix[0] += _warmth * 0.3;
      matrix[10] -= _warmth * 0.3;
    }

    return ColorFilter.matrix(matrix);
  }

  Widget _buildProcessingOverlay() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.transparent,
                Colors.white.withOpacity(0.2),
                Colors.white.withOpacity(0.4),
                Colors.white.withOpacity(0.2),
                Colors.transparent,
              ],
              stops: [
                math.max(0.0, _shimmerAnimation.value - 0.2),
                math.max(0.0, _shimmerAnimation.value - 0.1),
                _shimmerAnimation.value.clamp(0.0, 1.0),
                math.min(1.0, _shimmerAnimation.value + 0.1),
                math.min(1.0, _shimmerAnimation.value + 0.2),
              ],
            ),
          ),
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _processingText,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return Container(
      height: 180,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.8),
            Colors.black,
          ],
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 10),
          _buildModeSelector(),
          const SizedBox(height: 15),
          Expanded(
            child: _buildEditingControls(),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildModeSelector() {
    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: EditorMode.values.map((mode) {
          final isSelected = _currentMode == mode;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                setState(() {
                  _currentMode = mode;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  gradient: isSelected ? LinearGradient(
                    colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                  ) : null,
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Center(
                  child: Text(
                    _getModeTitle(mode),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEditingControls() {
    switch (_currentMode) {
      case EditorMode.filters:
        return _buildFiltersPanel();
      case EditorMode.adjust:
        return _buildAdjustPanel();
      case EditorMode.crop:
        return _buildCropPanel();
    }
  }

  Widget _buildFiltersPanel() {
    final filters = [
      {'name': 'Original', 'icon': Icons.image_outlined},
      {'name': 'Vintage', 'icon': Icons.camera_alt_outlined},
      {'name': 'B&W', 'icon': Icons.filter_b_and_w_outlined},
      {'name': 'Dramatic', 'icon': Icons.flash_on_outlined},
      {'name': 'Cool', 'icon': Icons.ac_unit_outlined},
      {'name': 'Warm', 'icon': Icons.wb_sunny_outlined},
      {'name': 'Soft', 'icon': Icons.blur_on_outlined},
      {'name': 'Vivid', 'icon': Icons.nature_outlined},
    ];

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['name'];

          return GestureDetector(
            onTap: () => _applyFilter(filter['name'] as String),
            child: Container(
              width: 70,
              margin: const EdgeInsets.only(right: 15),
              child: Column(
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: isSelected ? LinearGradient(
                        colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                      ) : null,
                      color: isSelected ? null : Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: isSelected
                          ? Colors.transparent
                          : Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      filter['icon'] as IconData,
                      color: Colors.white,
                      size: 22,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    filter['name'] as String,
                    style: TextStyle(
                      color: isSelected ? AppColors.lightPrimary : Colors.white,
                      fontSize: 12,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAdjustPanel() {
    final adjustments = [
      {'name': 'Brightness', 'value': _brightness, 'min': -1.0, 'max': 1.0, 'icon': Icons.brightness_6_rounded},
      {'name': 'Contrast', 'value': _contrast - 1.0, 'min': -1.0, 'max': 1.0, 'icon': Icons.contrast_rounded},
      {'name': 'Saturation', 'value': _saturation - 1.0, 'min': -1.0, 'max': 1.0, 'icon': Icons.palette_rounded},
      {'name': 'Warmth', 'value': _warmth, 'min': -1.0, 'max': 1.0, 'icon': Icons.wb_sunny_rounded},
    ];

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: adjustments.length,
        itemBuilder: (context, index) {
          final adjustment = adjustments[index];
          return Container(
            width: 70,
            margin: const EdgeInsets.only(right: 15),
            child: Column(
              children: [
                Icon(
                  adjustment['icon'] as IconData,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(height: 5),
                Text(
                  adjustment['name'] as String,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 5),
                Expanded(
                  child: RotatedBox(
                    quarterTurns: 3,
                    child: SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        activeTrackColor: AppColors.lightPrimary,
                        inactiveTrackColor: Colors.white.withOpacity(0.3),
                        thumbColor: AppColors.lightPrimary,
                        trackHeight: 3,
                        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                      ),
                      child: Slider(
                        value: (adjustment['value'] as double).clamp(
                          adjustment['min'] as double,
                          adjustment['max'] as double,
                        ),
                        min: adjustment['min'] as double,
                        max: adjustment['max'] as double,
                        onChanged: (value) {
                          HapticFeedback.selectionClick();
                          _updateAdjustment(adjustment['name'] as String, value);
                        },
                      ),
                    ),
                  ),
                ),
                Text(
                  (adjustment['value'] as double).toStringAsFixed(1),
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCropPanel() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.rotate_left_rounded, color: Colors.white, size: 18),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: AppColors.lightPrimary,
                      inactiveTrackColor: Colors.white.withOpacity(0.3),
                      thumbColor: AppColors.lightPrimary,
                      trackHeight: 3,
                      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
                    ),
                    child: Slider(
                      value: _rotation,
                      min: -45.0,
                      max: 45.0,
                      onChanged: (value) {
                        HapticFeedback.lightImpact();
                        setState(() => _rotation = value);
                      },
                    ),
                  ),
                ),
              ),
              Icon(Icons.rotate_right_rounded, color: Colors.white, size: 18),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildCropButton(
                icon: Icons.flip_rounded,
                label: 'Flip H',
                isActive: _flipHorizontal,
                onTap: () {
                  HapticFeedback.mediumImpact();
                  setState(() => _flipHorizontal = !_flipHorizontal);
                },
              ),
              _buildCropButton(
                icon: Icons.flip_camera_android_rounded,
                label: 'Flip V',
                isActive: _flipVertical,
                onTap: () {
                  HapticFeedback.mediumImpact();
                  setState(() => _flipVertical = !_flipVertical);
                },
              ),
              _buildCropButton(
                icon: Icons.refresh_rounded,
                label: 'Reset',
                isActive: false,
                onTap: _resetCrop,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCropButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          gradient: isActive ? LinearGradient(
            colors: [AppColors.lightPrimary, AppColors.lightSecondary],
          ) : null,
          color: isActive ? null : Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: isActive
              ? Colors.transparent
              : Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 18),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _applyFilter(String filterName) async {
    setState(() {
      _isProcessing = true;
      _processingText = 'Applying $filterName...';
      _selectedFilter = filterName;
    });

    _shimmerController.repeat();

    await Future.delayed(const Duration(milliseconds: 800));

    setState(() {
      switch (filterName) {
        case 'Original':
          _brightness = 0.0;
          _contrast = 1.0;
          _saturation = 1.0;
          _warmth = 0.0;
          break;
        case 'Vintage':
          _brightness = 0.1;
          _contrast = 1.2;
          _saturation = 0.8;
          _warmth = 0.3;
          break;
        case 'B&W':
          _brightness = 0.0;
          _contrast = 1.1;
          _saturation = 0.0;
          _warmth = 0.0;
          break;
        case 'Dramatic':
          _brightness = -0.05;
          _contrast = 1.4;
          _saturation = 1.3;
          _warmth = 0.0;
          break;
        case 'Cool':
          _brightness = 0.05;
          _contrast = 1.1;
          _saturation = 0.9;
          _warmth = -0.3;
          break;
        case 'Warm':
          _brightness = 0.1;
          _contrast = 1.1;
          _saturation = 1.1;
          _warmth = 0.4;
          break;
        case 'Soft':
          _brightness = 0.15;
          _contrast = 0.9;
          _saturation = 0.95;
          _warmth = 0.1;
          break;
        case 'Vivid':
          _brightness = 0.05;
          _contrast = 1.3;
          _saturation = 1.4;
          _warmth = 0.0;
          break;
      }
      _isProcessing = false;
    });

    _shimmerController.stop();
    HapticFeedback.mediumImpact();
  }

  void _updateAdjustment(String name, double value) {
    setState(() {
      switch (name) {
        case 'Brightness':
          _brightness = value;
          break;
        case 'Contrast':
          _contrast = value + 1.0;
          break;
        case 'Saturation':
          _saturation = value + 1.0;
          break;
        case 'Warmth':
          _warmth = value;
          break;
      }
    });
  }

  void _resetCrop() {
    setState(() {
      _rotation = 0.0;
      _flipHorizontal = false;
      _flipVertical = false;
    });
    HapticFeedback.mediumImpact();
  }

  String _getModeTitle(EditorMode mode) {
    switch (mode) {
      case EditorMode.filters:
        return 'Filters';
      case EditorMode.adjust:
        return 'Adjust';
      case EditorMode.crop:
        return 'Crop';
    }
  }

  void _saveImage() async {
    setState(() {
      _isProcessing = true;
      _processingText = 'Saving...';
    });

    _shimmerController.repeat();

    await Future.delayed(const Duration(milliseconds: 1500));

    _shimmerController.stop();

    HapticFeedback.mediumImpact();
    Navigator.pop(context);
  }
}

enum EditorMode {
  filters,
  adjust,
  crop,
}
