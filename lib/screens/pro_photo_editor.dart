import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'package:photo_manager/photo_manager.dart';
import 'package:pro_image_editor/pro_image_editor.dart';

import '../models/media_item.dart';

class ProPhotoEditor extends StatefulWidget {
  final MediaItem mediaItem;

  const ProPhotoEditor({
    super.key,
    required this.mediaItem,
  });

  @override
  State<ProPhotoEditor> createState() => _ProPhotoEditorState();
}

class _ProPhotoEditorState extends State<ProPhotoEditor> {
  Uint8List? _imageData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      if (widget.mediaItem.assetEntity != null) {
        final imageData = await widget.mediaItem.assetEntity!.originBytes;
        if (mounted && imageData != null) {
          setState(() {
            _imageData = imageData;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      );
    }

    if (_imageData == null) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: const Center(
          child: Icon(
            Icons.broken_image_rounded,
            color: Colors.white,
            size: 64,
          ),
        ),
      );
    }

    return ProImageEditor.memory(
      _imageData!,
      configs: ProImageEditorConfigs(
        designMode: ImageEditorDesignModeE.material,
        theme: ThemeData.dark(),
        i18n: const I18n(
          various: I18nVarious(
            loadingDialogMsg: 'Please wait...',
            closeEditorWarningTitle: 'Close Image Editor?',
            closeEditorWarningMessage: 'Are you sure you want to close the Image Editor? Your changes will not be saved.',
            closeEditorWarningConfirmBtn: 'OK',
            closeEditorWarningCancelBtn: 'Cancel',
          ),
          paintEditor: I18nPaintingEditor(
            bottomNavigationBarText: 'Paint',
            freestyle: 'Freestyle',
            arrow: 'Arrow',
            line: 'Line',
            rectangle: 'Rectangle',
            circle: 'Circle',
            dashLine: 'Dash line',
            lineWidth: 'Line width',
            toggleFill: 'Toggle fill',
            undo: 'Undo',
            redo: 'Redo',
            done: 'Done',
            back: 'Back',
            smallScreenMoreTooltip: 'More',
          ),
          textEditor: I18nTextEditor(
            bottomNavigationBarText: 'Text',
            inputHintText: 'Enter text',
            done: 'Done',
            back: 'Back',
            textAlign: 'Align text',
            backgroundMode: 'Background mode',
            smallScreenMoreTooltip: 'More',
          ),
          cropRotateEditor: I18nCropRotateEditor(
            bottomNavigationBarText: 'Crop/ Rotate',
            rotate: 'Rotate',
            ratio: 'Ratio',
            back: 'Back',
            done: 'Done',
            prepareImageDialogMsg: 'Please wait...',
            applyChangesDialogMsg: 'Please wait...',
            smallScreenMoreTooltip: 'More',
          ),
          filterEditor: I18nFilterEditor(
            bottomNavigationBarText: 'Filter',
            filters: I18nFilters(
              none: 'No Filter',
              addictiveBlue: 'AddictiveBlue',
              addictiveRed: 'AddictiveRed',
              aden: 'Aden',
              amaro: 'Amaro',
              ashby: 'Ashby',
              brannan: 'Brannan',
              brooklyn: 'Brooklyn',
              charmes: 'Charmes',
              clarendon: 'Clarendon',
              crema: 'Crema',
              dogpatch: 'Dogpatch',
              earlybird: 'Earlybird',
              f1977: 'F1977',
              gingham: 'Gingham',
              ginza: 'Ginza',
              hefe: 'Hefe',
              helena: 'Helena',
              hudson: 'Hudson',
              inkwell: 'Inkwell',
              juno: 'Juno',
              kelvin: 'Kelvin',
              lark: 'Lark',
              loFi: 'Lo-Fi',
              ludwig: 'Ludwig',
              maven: 'Maven',
              mayfair: 'Mayfair',
              moon: 'Moon',
              nashville: 'Nashville',
              perpetua: 'Perpetua',
              reyes: 'Reyes',
              rise: 'Rise',
              sierra: 'Sierra',
              skyline: 'Skyline',
              slumber: 'Slumber',
              stinson: 'Stinson',
              sutro: 'Sutro',
              toaster: 'Toaster',
              valencia: 'Valencia',
              vesper: 'Vesper',
              walden: 'Walden',
              willow: 'Willow',
              xProII: 'X-Pro II',
            ),
            back: 'Back',
            done: 'Done',
            smallScreenMoreTooltip: 'More',
          ),
          blurEditor: I18nBlurEditor(
            bottomNavigationBarText: 'Blur',
            back: 'Back',
            done: 'Done',
            smallScreenMoreTooltip: 'More',
          ),
          emojiEditor: I18nEmojiEditor(
            bottomNavigationBarText: 'Emoji',
            noRecents: 'No Recents',
            back: 'Back',
            done: 'Done',
            smallScreenMoreTooltip: 'More',
          ),
          stickerEditor: I18nStickerEditor(
            bottomNavigationBarText: 'Stickers',
            back: 'Back',
            done: 'Done',
            smallScreenMoreTooltip: 'More',
          ),
          cancel: 'Cancel',
          undo: 'Undo',
          redo: 'Redo',
          done: 'Done',
          remove: 'Remove',
          doneLoadingMsg: 'Changes are being applied',
        ),
        customWidgets: const ImageEditorCustomWidgets(),
        icons: const ImageEditorIcons(),
        paintEditorConfigs: const PaintEditorConfigs(
          enabled: true,
          hasOptionFreeStyle: true,
          hasOptionArrow: true,
          hasOptionLine: true,
          hasOptionRect: true,
          hasOptionCircle: true,
          hasOptionDashLine: true,
          canToggleFill: true,
          canChangeLineWidth: true,
          initialFill: false,
          showColorPicker: true,
          freeStyleHighPerformanceScaling: true,
          freeStyleHighPerformanceMoving: true,
        ),
        textEditorConfigs: const TextEditorConfigs(
          enabled: true,
          canToggleTextAlign: true,
          canToggleBackgroundMode: true,
          initFontSize: 24.0,
        ),
        cropRotateEditorConfigs: const CropRotateEditorConfigs(
          enabled: true,
          canRotate: true,
          canChangeAspectRatio: true,
          initAspectRatio: CropAspectRatios.custom,
        ),
        filterEditorConfigs: const FilterEditorConfigs(
          enabled: true,
          filterList: presetFiltersList,
        ),
        blurEditorConfigs: const BlurEditorConfigs(
          enabled: true,
          maxBlur: 5.0,
        ),
        emojiEditorConfigs: const EmojiEditorConfigs(
          enabled: true,
        ),
        stickerEditorConfigs: const StickerEditorConfigs(
          enabled: true,
        ),
        helperLines: const HelperLines(
          showVerticalLine: true,
          showHorizontalLine: true,
          showRotateLine: true,
        ),
        layerInteraction: const LayerInteraction(
          enabledHitDetection: true,
          rotateScaleRingThickness: 2,
        ),
      ),
      onImageEditingComplete: (Uint8List bytes) async {
        // Handle the edited image
        HapticFeedback.mediumImpact();
        Navigator.pop(context, bytes);
      },
      onCloseEditor: () {
        Navigator.pop(context);
      },
    );
  }
}
