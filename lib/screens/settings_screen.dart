import 'package:flutter/material.dart' hide ThemeMode;
import 'package:provider/provider.dart';

import '../providers/gallery_provider.dart';
import '../providers/security_provider.dart';
import '../providers/theme_provider.dart';
import '../utils/constants.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        elevation: 0,
      ),
      body: ListView(
        children: [
          _buildSection(
            title: 'Appearance',
            children: [
              _buildThemeSetting(context),
              _buildGridColumnsSetting(context),
            ],
          ),
          _buildSection(
            title: 'Security',
            children: [
              _buildSecuritySetting(context),
              _buildHiddenFolderSetting(context),
            ],
          ),
          _buildSection(
            title: 'Gallery',
            children: [
              _buildSortOrderSetting(context),
              _buildAutoBackupSetting(context),
            ],
          ),
          _buildSection(
            title: 'Premium',
            children: [
              _buildPremiumFeatures(context),
            ],
          ),
          _buildSection(
            title: 'About',
            children: [
              _buildAboutInfo(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
        ),
        ...children,
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildThemeSetting(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return ListTile(
          leading: const Icon(Icons.palette),
          title: const Text('Theme'),
          subtitle: Text(themeProvider.getThemeModeString()),
          onTap: () {
            _showThemeDialog(context, themeProvider);
          },
        );
      },
    );
  }

  Widget _buildGridColumnsSetting(BuildContext context) {
    return Consumer<GalleryProvider>(
      builder: (context, galleryProvider, child) {
        return ListTile(
          leading: const Icon(Icons.grid_on),
          title: const Text('Grid Columns'),
          subtitle: Text('${galleryProvider.gridColumns} columns'),
          onTap: () {
            _showGridColumnsDialog(context, galleryProvider);
          },
        );
      },
    );
  }

  Widget _buildSecuritySetting(BuildContext context) {
    return Consumer<SecurityProvider>(
      builder: (context, securityProvider, child) {
        return ListTile(
          leading: const Icon(Icons.security),
          title: const Text('Security'),
          subtitle: Text(securityProvider.getSecurityTypeString()),
          onTap: () {
            _showSecurityDialog(context, securityProvider);
          },
        );
      },
    );
  }

  Widget _buildHiddenFolderSetting(BuildContext context) {
    return Consumer<SecurityProvider>(
      builder: (context, securityProvider, child) {
        return SwitchListTile(
          secondary: const Icon(Icons.visibility_off),
          title: const Text('Hidden Folder'),
          subtitle: const Text('Protect private photos with authentication'),
          value: securityProvider.isHiddenFolderEnabled,
          onChanged: (value) {
            securityProvider.setHiddenFolderEnabled(value);
          },
        );
      },
    );
  }

  Widget _buildSortOrderSetting(BuildContext context) {
    return Consumer<GalleryProvider>(
      builder: (context, galleryProvider, child) {
        return ListTile(
          leading: const Icon(Icons.sort),
          title: const Text('Sort Order'),
          subtitle: Text(_getSortOrderString(galleryProvider.sortOrder)),
          onTap: () {
            _showSortOrderDialog(context, galleryProvider);
          },
        );
      },
    );
  }

  Widget _buildAutoBackupSetting(BuildContext context) {
    return SwitchListTile(
      secondary: const Icon(Icons.backup),
      title: const Text('Auto Backup'),
      subtitle: const Text('Automatically backup photos to cloud'),
      value: false, // TODO: Implement auto backup
      onChanged: (value) {
        // TODO: Implement auto backup
      },
    );
  }

  Widget _buildPremiumFeatures(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return ListTile(
          leading: const Icon(Icons.star),
          title: const Text('Premium Features'),
          subtitle: Text(themeProvider.isPremium ? 'Active' : 'Upgrade to unlock'),
          onTap: () {
            _showPremiumDialog(context, themeProvider);
          },
        );
      },
    );
  }

  Widget _buildAboutInfo(BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.info),
      title: const Text('About'),
      subtitle: Text('Version ${AppConstants.appVersion}'),
      onTap: () {
        _showAboutDialog(context);
      },
    );
  }

  void _showThemeDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Light'),
              leading: Radio<AppThemeMode>(
                value: AppThemeMode.light,
                groupValue: themeProvider.themeMode,
                onChanged: (value) {
                  themeProvider.setThemeMode(value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('Dark'),
              leading: Radio<AppThemeMode>(
                value: AppThemeMode.dark,
                groupValue: themeProvider.themeMode,
                onChanged: (value) {
                  themeProvider.setThemeMode(value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('System'),
              leading: Radio<AppThemeMode>(
                value: AppThemeMode.system,
                groupValue: themeProvider.themeMode,
                onChanged: (value) {
                  themeProvider.setThemeMode(value!);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showGridColumnsDialog(BuildContext context, GalleryProvider galleryProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Grid Columns'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [2, 3, 4, 5].map((columns) {
            return ListTile(
              title: Text('$columns columns'),
              leading: Radio<int>(
                value: columns,
                groupValue: galleryProvider.gridColumns,
                onChanged: (value) {
                  galleryProvider.setGridColumns(value!);
                  Navigator.pop(context);
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showSecurityDialog(BuildContext context, SecurityProvider securityProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Security Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('PIN'),
              subtitle: const Text('Use a 4-digit PIN'),
              onTap: () {
                Navigator.pop(context);
                _showPinSetupDialog(context, securityProvider);
              },
            ),
            ListTile(
              title: const Text('Pattern'),
              subtitle: const Text('Draw a pattern to unlock'),
              onTap: () {
                Navigator.pop(context);
                _showPatternSetupDialog(context, securityProvider);
              },
            ),
            ListTile(
              title: const Text('Fingerprint'),
              subtitle: const Text('Use fingerprint authentication'),
              onTap: () {
                Navigator.pop(context);
                securityProvider.setFingerprintEnabled(true);
              },
            ),
            ListTile(
              title: const Text('Disable Security'),
              subtitle: const Text('Remove all security'),
              onTap: () {
                Navigator.pop(context);
                securityProvider.disableSecurity();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSortOrderDialog(BuildContext context, GalleryProvider galleryProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Order'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Date'),
              leading: Radio<SortOrder>(
                value: SortOrder.date,
                groupValue: galleryProvider.sortOrder,
                onChanged: (value) {
                  galleryProvider.setSortOrder(value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('Name'),
              leading: Radio<SortOrder>(
                value: SortOrder.name,
                groupValue: galleryProvider.sortOrder,
                onChanged: (value) {
                  galleryProvider.setSortOrder(value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('Size'),
              leading: Radio<SortOrder>(
                value: SortOrder.size,
                groupValue: galleryProvider.sortOrder,
                onChanged: (value) {
                  galleryProvider.setSortOrder(value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('Type'),
              leading: Radio<SortOrder>(
                value: SortOrder.type,
                groupValue: galleryProvider.sortOrder,
                onChanged: (value) {
                  galleryProvider.setSortOrder(value!);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPremiumDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Premium Features'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Unlock all premium features:'),
            const SizedBox(height: 16),
            ...AppConstants.premiumFeatures.map((feature) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    const Icon(Icons.check, color: Colors.green, size: 16),
                    const SizedBox(width: 8),
                    Text(feature),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement premium purchase
              themeProvider.setPremium(true);
              Navigator.pop(context);
            },
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppConstants.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          Icons.photo_library,
          color: Colors.white,
        ),
      ),
      children: const [
        Text('A comprehensive gallery app with advanced features for managing your photos and videos.'),
      ],
    );
  }

  void _showPinSetupDialog(BuildContext context, SecurityProvider securityProvider) {
    // TODO: Implement PIN setup dialog
  }

  void _showPatternSetupDialog(BuildContext context, SecurityProvider securityProvider) {
    // TODO: Implement pattern setup dialog
  }

  String _getSortOrderString(SortOrder sortOrder) {
    switch (sortOrder) {
      case SortOrder.date:
        return 'Date';
      case SortOrder.name:
        return 'Name';
      case SortOrder.size:
        return 'Size';
      case SortOrder.type:
        return 'Type';
    }
  }
} 