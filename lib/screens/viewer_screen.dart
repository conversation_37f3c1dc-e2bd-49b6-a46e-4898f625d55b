import 'dart:io';

import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view.dart'as pv;
import 'package:photo_view/photo_view_gallery.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';
import 'package:provider/provider.dart';

import '../models/media_item.dart';
import '../providers/gallery_provider.dart';
import '../utils/constants.dart';

class ViewerScreen extends StatefulWidget {
  final List<MediaItem> mediaItems;
  final int initialIndex;

  const ViewerScreen({
    super.key,
    required this.mediaItems,
    required this.initialIndex,
  });

  @override
  State<ViewerScreen> createState() => _ViewerScreenState();
}

class _ViewerScreenState extends State<ViewerScreen> {
  late PageController _pageController;
  late int _currentIndex;
  bool _showControls = true;
  ChewieController? _chewieController;
  VideoPlayerController? _videoPlayerController;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    _initializeVideoPlayer();
  }

  void _initializeVideoPlayer() async {
    final currentItem = widget.mediaItems[_currentIndex];
    if (currentItem.type == MediaType.video && currentItem.assetEntity != null) {
      try {
        final file = await currentItem.assetEntity!.file;
        if (file != null) {
          _videoPlayerController = VideoPlayerController.file(file);
          _chewieController = ChewieController(
            videoPlayerController: _videoPlayerController!,
            autoPlay: true,
            looping: false,
            allowFullScreen: true,
            allowMuting: true,
          );
          setState(() {});
        }
      } catch (e) {
        debugPrint('Error loading video file: $e');
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _chewieController?.dispose();
    _videoPlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '${_currentIndex + 1} / ${widget.mediaItems.length}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),

      ),
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: widget.mediaItems.length,
              itemBuilder: (context, index) {
                final item = widget.mediaItems[index];
                return _buildMediaView(item);
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: const Icon(Icons.favorite_border, color: Colors.white),
                  onPressed: _toggleFavorite,
                ),
                IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: _shareMedia,
                ),
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.white),
                  onPressed: _editMedia,
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: _deleteMedia,
                ),
              ],
            ),
          ),

          // Bottom controls

        ],
      ),
    );
  }

  Widget _buildMediaView(MediaItem item) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
      },
      child: Center(
        child: item.type == MediaType.image
            ? _buildImageView(item)
            : _buildVideoView(item),
      ),
    );
  }

  Widget _buildImageView(MediaItem item) {
    if (item.assetEntity != null) {
      return FutureBuilder<File?>(
        future: item.assetEntity!.file,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
            return PhotoView(
              imageProvider: FileImage(snapshot.data!),
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 2,
              backgroundDecoration: const BoxDecoration(color: Colors.black),
              loadingBuilder: (context, event) => const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
              errorBuilder: (context, error, stackTrace) => const Center(
                child: Icon(Icons.broken_image, color: Colors.white, size: 64),
              ),
            );
          } else if (snapshot.hasError) {
            return const Center(
              child: Icon(Icons.broken_image, color: Colors.white, size: 64),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(color: Colors.white),
            );
          }
        },
      );
    } else {
      return const Center(
        child: Icon(Icons.broken_image, color: Colors.white, size: 64),
      );
    }
  }

  Widget _buildVideoView(MediaItem item) {
    if (_chewieController != null) {
      return Chewie(controller: _chewieController!);
    } else {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Dispose previous video controller
    _chewieController?.dispose();
    _videoPlayerController?.dispose();

    // Initialize new video controller
    _initializeVideoPlayer();
  }

  void _onMenuSelected(String value) {
    switch (value) {
      case 'favorite':
        _toggleFavorite();
        break;
      case 'share':
        _shareMedia();
        break;
      case 'edit':
        _editMedia();
        break;
      case 'delete':
        _deleteMedia();
        break;
    }
  }

  void _toggleFavorite() {
    final galleryProvider = Provider.of<GalleryProvider>(context, listen: false);
    final currentItem = widget.mediaItems[_currentIndex];
    galleryProvider.toggleFavorite(currentItem);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          galleryProvider.isFavorite(currentItem.id)
              ? 'Added to favorites'
              : 'Removed from favorites',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _shareMedia() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality coming soon')),
    );
  }

  void _editMedia() {
    // TODO: Implement edit functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit functionality coming soon')),
    );
  }

  void _deleteMedia() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Media'),
        content: const Text('Are you sure you want to delete this item?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final galleryProvider = Provider.of<GalleryProvider>(context, listen: false);
              final currentItem = widget.mediaItems[_currentIndex];
              galleryProvider.deleteItems([currentItem.id]);
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}