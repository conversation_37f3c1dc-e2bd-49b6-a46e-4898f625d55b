import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view.dart'as pv;
import 'package:photo_view/photo_view_gallery.dart';
import 'package:chewie/chewie.dart';
import 'package:simple_gallery/widgets/perfect_media_grid.dart';
import 'package:video_player/video_player.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';

import '../models/media_item.dart';
import '../providers/gallery_provider.dart';
import '../utils/constants.dart';

class ViewerScreen extends StatefulWidget {
  final List<MediaItem> mediaItems;
  final int initialIndex;

  const ViewerScreen({
    super.key,
    required this.mediaItems,
    required this.initialIndex,
  });

  @override
  State<ViewerScreen> createState() => _ViewerScreenState();
}

class _ViewerScreenState extends State<ViewerScreen> {
  late PageController _pageController;
  late int _currentIndex;
  bool _showControls = true;
  ChewieController? _chewieController;
  VideoPlayerController? _videoPlayerController;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    _initializeVideoPlayer();
  }

  void _initializeVideoPlayer() async {
    final currentItem = widget.mediaItems[_currentIndex];
    if (currentItem.type == MediaType.video && currentItem.assetEntity != null) {
      try {
        final file = await currentItem.assetEntity!.file;
        if (file != null) {
          _videoPlayerController = VideoPlayerController.file(file);
          _chewieController = ChewieController(
            videoPlayerController: _videoPlayerController!,
            autoPlay: true,
            looping: false,
            allowFullScreen: true,
            allowMuting: true,
          );
          setState(() {});
        }
      } catch (e) {
        debugPrint('Error loading video file: $e');
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _chewieController?.dispose();
    _videoPlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '${_currentIndex + 1} / ${widget.mediaItems.length}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),

      ),
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: widget.mediaItems.length,
              itemBuilder: (context, index) {
                final item = widget.mediaItems[index];
                return _buildMediaView(item);
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: const Icon(Icons.favorite_border, color: Colors.white),
                  onPressed: _toggleFavorite,
                ),
                IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: _shareMedia,
                ),
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.white),
                  onPressed: (){
                    openPhotoEditor(widget.mediaItems[widget.initialIndex], context);
                  }
                  ,
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.white),
                  onPressed: _deleteMedia,
                ),
              ],
            ),
          ),

          // Bottom controls

        ],
      ),
    );
  }

  Widget _buildMediaView(MediaItem item) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
      },
      child: Center(
        child: item.type == MediaType.image
            ? _buildImageView(item)
            : _buildVideoView(item),
      ),
    );
  }

  Widget _buildImageView(MediaItem item) {
    if (item.assetEntity != null) {
      return FutureBuilder<File?>(
        future: item.assetEntity!.file,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
            return PhotoView(
              imageProvider: FileImage(snapshot.data!),
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 2,
              backgroundDecoration: const BoxDecoration(color: Colors.black),
              loadingBuilder: (context, event) => const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
              errorBuilder: (context, error, stackTrace) => const Center(
                child: Icon(Icons.broken_image, color: Colors.white, size: 64),
              ),
            );
          } else if (snapshot.hasError) {
            return const Center(
              child: Icon(Icons.broken_image, color: Colors.white, size: 64),
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(color: Colors.white),
            );
          }
        },
      );
    } else {
      return const Center(
        child: Icon(Icons.broken_image, color: Colors.white, size: 64),
      );
    }
  }

  Widget _buildVideoView(MediaItem item) {
    if (_chewieController != null) {
      return Chewie(controller: _chewieController!);
    } else {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Dispose previous video controller
    _chewieController?.dispose();
    _videoPlayerController?.dispose();

    // Initialize new video controller
    _initializeVideoPlayer();
  }

  void _onMenuSelected(String value) {
    switch (value) {
      case 'favorite':
        _toggleFavorite();
        break;
      case 'share':
        _shareMedia();
        break;
      case 'edit':
        _editMedia();
        break;
      case 'delete':
        _deleteMedia();
        break;
    }
  }

  void _toggleFavorite() {
    final galleryProvider = Provider.of<GalleryProvider>(context, listen: false);
    final currentItem = widget.mediaItems[_currentIndex];
    galleryProvider.toggleFavorite(currentItem);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          galleryProvider.isFavorite(currentItem.id)
              ? 'Added to favorites'
              : 'Removed from favorites',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _shareMedia() async {
    try {
      final currentItem = widget.mediaItems[_currentIndex];
      if (currentItem.assetEntity != null) {
        // Show loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                ),
                SizedBox(width: 12),
                Text('Preparing to share...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );

        // Get the file from the asset
        final file = await currentItem.assetEntity!.file;
        if (file != null) {
          // Share the file
          await Share.shareXFiles(
            [XFile(file.path)],
            text: currentItem.type == MediaType.image
              ? 'Shared from Gallery'
              : 'Video shared from Gallery',
          );
          HapticFeedback.mediumImpact();

          // Hide loading and show success
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  Text('${currentItem.type == MediaType.image ? 'Image' : 'Video'} shared successfully!'),
                ],
              ),
              backgroundColor: Colors.green.withOpacity(0.8),
              duration: const Duration(seconds: 2),
            ),
          );
        } else {
          _showErrorSnackBar('Unable to access this ${currentItem.type == MediaType.image ? 'image' : 'video'}');
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      _showErrorSnackBar('Error sharing ${widget.mediaItems[_currentIndex].type == MediaType.image ? 'image' : 'video'}: ${e.toString()}');
    }
  }

  void _editMedia() {
    // TODO: Implement edit functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit functionality coming soon')),
    );
  }

  void _deleteMedia() {
    final currentItem = widget.mediaItems[_currentIndex];
    final isImage = currentItem.type == MediaType.image;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(
              Icons.delete_forever,
              color: Colors.red,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text('Delete ${isImage ? 'Image' : 'Video'}'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to permanently delete this ${isImage ? 'image' : 'video'}?'),
            const SizedBox(height: 8),
            Text(
              'This action cannot be undone.',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performDelete(currentItem, isImage);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDelete(MediaItem currentItem, bool isImage) async {
    try {
      // Show loading
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
              ),
              const SizedBox(width: 12),
              Text('Deleting ${isImage ? 'image' : 'video'}...'),
            ],
          ),
          duration: const Duration(seconds: 3),
        ),
      );

      // Delete the item
      final galleryProvider = Provider.of<GalleryProvider>(context, listen: false);
      await galleryProvider.deleteItems([currentItem.id]);

      HapticFeedback.mediumImpact();

      // Hide loading and show success
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text('${isImage ? 'Image' : 'Video'} deleted successfully'),
            ],
          ),
          backgroundColor: Colors.green.withOpacity(0.8),
          duration: const Duration(seconds: 2),
        ),
      );

      // Navigate back if this was the only item or last item
      if (widget.mediaItems.length <= 1) {
        Navigator.pop(context);
      } else {
        // Update the current index if needed
        if (_currentIndex >= widget.mediaItems.length - 1) {
          setState(() {
            _currentIndex = widget.mediaItems.length - 2;
          });
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
      _showErrorSnackBar('Failed to delete ${isImage ? 'image' : 'video'}: ${e.toString()}');
    }
  }
}