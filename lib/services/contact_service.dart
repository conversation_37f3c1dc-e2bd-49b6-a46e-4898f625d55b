import 'dart:convert';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/contact.dart';
import '../constants/app_constants.dart';

class ContactService {
  static final ContactService _instance = ContactService._internal();
  factory ContactService() => _instance;
  ContactService._internal();

  List<Contact> _contacts = [];
  List<Contact> _favorites = [];
  List<String> _blockedNumbers = [];

  List<Contact> get contacts => _contacts;
  List<Contact> get favorites => _favorites;
  List<String> get blockedNumbers => _blockedNumbers;

  Future<bool> requestPermissions() async {
    final status = await Permission.contacts.request();
    return status.isGranted;
  }

  Future<void> loadContacts() async {
    try {
      if (!await requestPermissions()) {
        throw Exception('Contacts permission not granted');
      }

      // TODO: Implement actual contact loading when contacts_service package is fixed
      // For now, use mock data
      _contacts = _generateMockContacts();

      await _loadFavorites();
      await _loadBlockedNumbers();
    } catch (e) {
      throw Exception('Failed to load contacts: $e');
    }
  }

  List<Contact> _generateMockContacts() {
    return [
      Contact(
        id: '1',
        name: 'John Doe',
        phoneNumbers: ['+1234567890'],
        email: '<EMAIL>',
      ),
      Contact(
        id: '2',
        name: 'Jane Smith',
        phoneNumbers: ['+1987654321'],
        email: '<EMAIL>',
      ),
      Contact(
        id: '3',
        name: 'Bob Johnson',
        phoneNumbers: ['+1555123456'],
        email: '<EMAIL>',
      ),
      Contact(
        id: '4',
        name: 'Alice Brown',
        phoneNumbers: ['+1444987654'],
        email: '<EMAIL>',
      ),
      Contact(
        id: '5',
        name: 'Charlie Wilson',
        phoneNumbers: ['+1333456789'],
        email: '<EMAIL>',
      ),
    ];
  }

  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = prefs.getStringList(AppConstants.favoritesKey) ?? [];
    
    _favorites = _contacts.where((contact) {
      return favoritesJson.contains(contact.id);
    }).toList();
  }

  Future<void> _loadBlockedNumbers() async {
    final prefs = await SharedPreferences.getInstance();
    _blockedNumbers = prefs.getStringList(AppConstants.blockedNumbersKey) ?? [];
  }

  Future<void> addToFavorites(Contact contact) async {
    if (!_favorites.contains(contact)) {
      _favorites.add(contact);
      await _saveFavorites();
    }
  }

  Future<void> removeFromFavorites(Contact contact) async {
    _favorites.removeWhere((c) => c.id == contact.id);
    await _saveFavorites();
  }

  Future<void> _saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesIds = _favorites.map((c) => c.id).toList();
    await prefs.setStringList(AppConstants.favoritesKey, favoritesIds);
  }

  Future<void> blockNumber(String phoneNumber) async {
    if (!_blockedNumbers.contains(phoneNumber)) {
      _blockedNumbers.add(phoneNumber);
      await _saveBlockedNumbers();
    }
  }

  Future<void> unblockNumber(String phoneNumber) async {
    _blockedNumbers.remove(phoneNumber);
    await _saveBlockedNumbers();
  }

  Future<void> _saveBlockedNumbers() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(AppConstants.blockedNumbersKey, _blockedNumbers);
  }

  bool isNumberBlocked(String phoneNumber) {
    return _blockedNumbers.contains(phoneNumber);
  }

  List<Contact> searchContacts(String query) {
    if (query.isEmpty) return _contacts;
    
    final lowercaseQuery = query.toLowerCase();
    return _contacts.where((contact) {
      // Search by name
      if (contact.name.toLowerCase().contains(lowercaseQuery)) {
        return true;
      }
      
      // Search by phone number
      for (final phone in contact.phoneNumbers) {
        if (phone.contains(query)) {
          return true;
        }
      }
      
      // T9 search
      if (_t9Search(contact.name, query)) {
        return true;
      }
      
      return false;
    }).toList();
  }

  bool _t9Search(String name, String query) {
    final lowercaseName = name.toLowerCase();
    final words = lowercaseName.split(' ');
    
    for (final word in words) {
      if (word.isEmpty) continue;
      
      String t9Code = '';
      for (int i = 0; i < word.length; i++) {
        final char = word[i];
        for (final entry in AppConstants.t9Mapping.entries) {
          if (entry.value.contains(char)) {
            t9Code += entry.key;
            break;
          }
        }
      }
      
      if (t9Code.startsWith(query)) {
        return true;
      }
    }
    
    return false;
  }

  Contact? findContactByNumber(String phoneNumber) {
    return _contacts.firstWhere(
      (contact) => contact.phoneNumbers.contains(phoneNumber),
      orElse: () => Contact(
        id: '',
        name: 'Unknown',
        phoneNumbers: [phoneNumber],
      ),
    );
  }

  List<Contact> getRecentContacts({int limit = 10}) {
    final sortedContacts = List<Contact>.from(_contacts);
    sortedContacts.sort((a, b) {
      final aTime = a.lastCallTime ?? DateTime(1900);
      final bTime = b.lastCallTime ?? DateTime(1900);
      return bTime.compareTo(aTime);
    });
    
    return sortedContacts.take(limit).toList();
  }

  Future<void> updateContactCallTime(String contactId, DateTime callTime) async {
    final index = _contacts.indexWhere((c) => c.id == contactId);
    if (index != -1) {
      final contact = _contacts[index];
      _contacts[index] = contact.copyWith(
        lastCallTime: callTime,
        callCount: contact.callCount + 1,
      );
    }
  }

  void clearCache() {
    _contacts.clear();
    _favorites.clear();
    _blockedNumbers.clear();
  }
} 