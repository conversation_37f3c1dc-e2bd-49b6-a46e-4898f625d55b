import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

/// Service for handling gallery app permissions
/// Manages photos, videos, camera, and storage permissions for cross-platform gallery
class GalleryPermissionService {
  /// Check if photos permission is granted
  Future<bool> hasPhotosPermission() async {
    if (Platform.isAndroid) {
      // For Android 13+ use granular media permissions
      final status = await Permission.photos.status;
      return status.isGranted;
    } else {
      // For iOS use photo library permission
      final status = await Permission.photos.status;
      return status.isGranted;
    }
  }

  /// Check if videos permission is granted
  Future<bool> hasVideosPermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.videos.status;
      return status.isGranted;
    } else {
      // iOS uses same permission for photos and videos
      final status = await Permission.photos.status;
      return status.isGranted;
    }
  }

  /// Check if camera permission is granted
  Future<bool> hasCameraPermission() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  /// Check if storage permission is granted (for older Android versions)
  Future<bool> hasStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.status;
      return status.isGranted;
    }
    return true; // iOS doesn't need explicit storage permission
  }

  /// Check if notification permission is granted
  Future<bool> hasNotificationPermission() async {
    final status = await Permission.notification.status;
    return status.isGranted;
  }

  /// Request photos permission
  Future<bool> requestPhotosPermission() async {
    final status = await Permission.photos.request();
    return status.isGranted;
  }

  /// Request videos permission
  Future<bool> requestVideosPermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.videos.request();
      return status.isGranted;
    } else {
      // iOS uses same permission for photos and videos
      final status = await Permission.photos.request();
      return status.isGranted;
    }
  }

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// Request storage permission
  Future<bool> requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    return true; // iOS doesn't need explicit storage permission
  }

  /// Request notification permission
  Future<bool> requestNotificationPermission() async {
    final status = await Permission.notification.request();
    return status.isGranted;
  }

  /// Request all gallery-related permissions
  Future<Map<String, bool>> requestAllGalleryPermissions() async {
    final results = <String, bool>{};
    
    // Essential permissions
    results['photos'] = await requestPhotosPermission();
    results['videos'] = await requestVideosPermission();
    results['camera'] = await requestCameraPermission();
    
    // Platform-specific permissions
    if (Platform.isAndroid) {
      results['storage'] = await requestStoragePermission();
    }
    
    // Optional permissions
    results['notifications'] = await requestNotificationPermission();
    
    return results;
  }

  /// Check if all essential permissions are granted
  Future<bool> hasAllEssentialPermissions() async {
    final photos = await hasPhotosPermission();
    final videos = await hasVideosPermission();
    final camera = await hasCameraPermission();
    
    bool storage = true;
    if (Platform.isAndroid) {
      storage = await hasStoragePermission();
    }
    
    return photos && videos && camera && storage;
  }

  /// Get permission status with detailed information
  Future<PermissionStatus> getDetailedPermissionStatus(Permission permission) async {
    return await permission.status;
  }

  /// Check if permission is permanently denied
  Future<bool> isPermissionPermanentlyDenied(Permission permission) async {
    final status = await permission.status;
    return status.isPermanentlyDenied;
  }

  /// Open app settings for permission management
  Future<bool> openAppSettings() async {
    return await openAppSettings();
  }

  /// Get platform-specific permission requirements
  List<Permission> getRequiredPermissions() {
    final permissions = <Permission>[
      Permission.photos,
      Permission.camera,
    ];
    
    if (Platform.isAndroid) {
      permissions.addAll([
        Permission.videos,
        Permission.storage,
      ]);
    }
    
    return permissions;
  }

  /// Get optional permissions
  List<Permission> getOptionalPermissions() {
    return [
      Permission.notification,
    ];
  }

  /// Request permission with rationale handling
  Future<PermissionRequestResult> requestPermissionWithRationale(Permission permission) async {
    final currentStatus = await permission.status;
    
    if (currentStatus.isGranted) {
      return PermissionRequestResult.granted;
    }
    
    if (currentStatus.isPermanentlyDenied) {
      return PermissionRequestResult.permanentlyDenied;
    }
    
    final newStatus = await permission.request();
    
    if (newStatus.isGranted) {
      return PermissionRequestResult.granted;
    } else if (newStatus.isPermanentlyDenied) {
      return PermissionRequestResult.permanentlyDenied;
    } else {
      return PermissionRequestResult.denied;
    }
  }
}

/// Result of permission request
enum PermissionRequestResult {
  granted,
  denied,
  permanentlyDenied,
}

/// Permission information for UI display
class PermissionInfo {
  final Permission permission;
  final String title;
  final String description;
  final String rationale;
  final bool isRequired;
  final bool isGranted;

  const PermissionInfo({
    required this.permission,
    required this.title,
    required this.description,
    required this.rationale,
    required this.isRequired,
    required this.isGranted,
  });
}
