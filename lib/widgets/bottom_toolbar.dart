import 'package:flutter/material.dart';

import '../utils/constants.dart';

class BottomToolbar extends StatelessWidget {
  final VoidCallback onSelect;
  final VoidCallback onSort;
  final VoidCallback onFilter;
  final VoidCallback onView;

  const BottomToolbar({
    super.key,
    required this.onSelect,
    required this.onSort,
    required this.onFilter,
    required this.onView,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            
            // Toolbar buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildToolbarButton(
                  icon: Icons.select_all,
                  label: 'Select',
                  onTap: onSelect,
                ),
                _buildToolbarButton(
                  icon: Icons.sort,
                  label: 'Sort',
                  onTap: onSort,
                ),
                _buildToolbarButton(
                  icon: Icons.filter_list,
                  label: 'Filter',
                  onTap: onFilter,
                ),
                _buildToolbarButton(
                  icon: Icons.view_module,
                  label: 'View',
                  onTap: onView,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppConstants.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppConstants.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
} 