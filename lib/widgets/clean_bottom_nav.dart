import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../providers/theme_provider.dart';

class CleanBottomNav extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<CleanNavItem> items;

  const CleanBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  });

  @override
  State<CleanBottomNav> createState() => _CleanBottomNavState();
}

class _CleanBottomNavState extends State<CleanBottomNav>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<AnimationController> _itemControllers;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _itemControllers = List.generate(
      widget.items.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 200),
        vsync: this,
      ),
    );

    _animationController.forward();
    _itemControllers[widget.currentIndex].forward();
  }

  @override
  void didUpdateWidget(CleanBottomNav oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _itemControllers[oldWidget.currentIndex].reverse();
      _itemControllers[widget.currentIndex].forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkSurface : AppColors.lightSurface,
        border: Border(
          top: BorderSide(
            color: isDark 
              ? Colors.white.withOpacity(0.1)
              : Colors.black.withOpacity(0.1),
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Container(
          height: 65,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: widget.items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == widget.currentIndex;

              return _buildNavItem(
                item: item,
                isSelected: isSelected,
                index: index,
                onTap: () {
                  HapticFeedback.lightImpact();
                  widget.onTap(index);
                },
                isDark: isDark,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required CleanNavItem item,
    required bool isSelected,
    required int index,
    required VoidCallback onTap,
    required bool isDark,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: AnimatedBuilder(
          animation: _itemControllers[index],
          builder: (context, child) {
            final scale = 1.0 + (_itemControllers[index].value * 0.1);
            
            return Transform.scale(
              scale: scale,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Icon with indicator
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        // Background indicator
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          width: isSelected ? 40 : 0,
                          height: isSelected ? 40 : 0,
                          decoration: BoxDecoration(
                            gradient: isSelected ? LinearGradient(
                              colors: [
                                AppColors.lightPrimary.withOpacity(0.2),
                                AppColors.lightSecondary.withOpacity(0.2),
                              ],
                            ) : null,
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        // Icon
                        AnimatedSwitcher(
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            isSelected ? item.activeIcon : item.icon,
                            key: ValueKey(isSelected),
                            color: isSelected
                              ? AppColors.lightPrimary
                              : (isDark 
                                  ? AppColors.darkOnSurface.withOpacity(0.6) 
                                  : AppColors.lightOnSurface.withOpacity(0.6)),
                            size: isSelected ? 26 : 24,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    // Label
                    AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 200),
                      style: TextStyle(
                        color: isSelected
                          ? AppColors.lightPrimary
                          : (isDark 
                              ? AppColors.darkOnSurface.withOpacity(0.6) 
                              : AppColors.lightOnSurface.withOpacity(0.6)),
                        fontSize: isSelected ? 12 : 11,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      ),
                      child: Text(
                        item.label,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class CleanNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  const CleanNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}
