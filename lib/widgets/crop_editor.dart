import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../providers/theme_provider.dart';

class CropEditor extends StatefulWidget {
  final double rotation;
  final bool flipHorizontal;
  final bool flipVertical;
  final Function(double) onRotationChanged;
  final VoidCallback onFlipHorizontal;
  final VoidCallback onFlipVertical;
  final VoidCallback onReset;

  const CropEditor({
    super.key,
    required this.rotation,
    required this.flipHorizontal,
    required this.flipVertical,
    required this.onRotationChanged,
    required this.onFlipHorizontal,
    required this.onFlipVertical,
    required this.onReset,
  });

  @override
  State<CropEditor> createState() => _CropEditorState();
}

class _CropEditorState extends State<CropEditor> {
  final List<CropRatio> _cropRatios = [
    CropRatio('Free', null, Icons.crop_free_rounded),
    CropRatio('1:1', 1.0, Icons.crop_square_rounded),
    CropRatio('4:3', 4.0 / 3.0, Icons.crop_landscape_rounded),
    CropRatio('3:4', 3.0 / 4.0, Icons.crop_portrait_rounded),
    CropRatio('16:9', 16.0 / 9.0, Icons.crop_16_9_rounded),
    CropRatio('9:16', 9.0 / 16.0, Icons.crop_portrait_rounded),
    CropRatio('3:2', 3.0 / 2.0, Icons.crop_landscape_rounded),
    CropRatio('2:3', 2.0 / 3.0, Icons.crop_portrait_rounded),
  ];

  CropRatio? _selectedRatio;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      child: Column(
        children: [
          // Crop ratio selection
          Container(
            height: 60,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: _cropRatios.length,
              itemBuilder: (context, index) {
                final ratio = _cropRatios[index];
                final isSelected = _selectedRatio == ratio;
                
                return GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _selectedRatio = isSelected ? null : ratio;
                    });
                  },
                  child: Container(
                    width: 60,
                    margin: const EdgeInsets.only(right: 12),
                    child: Column(
                      children: [
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            gradient: isSelected ? LinearGradient(
                              colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                            ) : null,
                            color: isSelected ? null : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: isSelected 
                                ? Colors.transparent
                                : Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: AppColors.lightPrimary.withOpacity(0.4),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ] : null,
                          ),
                          child: Icon(
                            ratio.icon,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          ratio.name,
                          style: TextStyle(
                            color: isSelected ? AppColors.lightPrimary : Colors.white,
                            fontSize: 10,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Rotation and flip controls
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                // Rotation slider
                Row(
                  children: [
                    Icon(
                      Icons.rotate_left_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor: AppColors.lightPrimary,
                            inactiveTrackColor: Colors.white.withOpacity(0.3),
                            thumbColor: AppColors.lightPrimary,
                            overlayColor: AppColors.lightPrimary.withOpacity(0.2),
                            thumbShape: const RoundSliderThumbShape(
                              enabledThumbRadius: 10,
                            ),
                            trackHeight: 3,
                          ),
                          child: Slider(
                            value: widget.rotation,
                            min: -45.0,
                            max: 45.0,
                            divisions: 90,
                            onChanged: widget.onRotationChanged,
                          ),
                        ),
                      ),
                    ),
                    Icon(
                      Icons.rotate_right_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ],
                ),
                
                Text(
                  '${widget.rotation.toStringAsFixed(1)}°',
                  style: TextStyle(
                    color: AppColors.lightPrimary,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.flip_rounded,
                      label: 'Flip H',
                      isActive: widget.flipHorizontal,
                      onTap: widget.onFlipHorizontal,
                    ),
                    _buildActionButton(
                      icon: Icons.flip_camera_android_rounded,
                      label: 'Flip V',
                      isActive: widget.flipVertical,
                      onTap: widget.onFlipVertical,
                    ),
                    _buildActionButton(
                      icon: Icons.refresh_rounded,
                      label: 'Reset',
                      isActive: false,
                      onTap: widget.onReset,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: isActive ? LinearGradient(
            colors: [AppColors.lightPrimary, AppColors.lightSecondary],
          ) : null,
          color: isActive ? null : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive 
              ? Colors.transparent
              : Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: isActive ? [
            BoxShadow(
              color: AppColors.lightPrimary.withOpacity(0.4),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CropRatio {
  final String name;
  final double? ratio;
  final IconData icon;

  const CropRatio(this.name, this.ratio, this.icon);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CropRatio && 
           other.name == name && 
           other.ratio == ratio;
  }

  @override
  int get hashCode => name.hashCode ^ ratio.hashCode;
}
