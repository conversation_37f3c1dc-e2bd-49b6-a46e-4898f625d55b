import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import 'dart:typed_data';

class CropWidget extends StatefulWidget {
  final Uint8List imageData;
  final double rotation;
  final bool flipHorizontal;
  final bool flipVertical;
  final Function(Rect) onCropChanged;

  const CropWidget({
    super.key,
    required this.imageData,
    required this.rotation,
    required this.flipHorizontal,
    required this.flipVertical,
    required this.onCropChanged,
  });

  @override
  State<CropWidget> createState() => _CropWidgetState();
}

class _CropWidgetState extends State<CropWidget> {
  Rect _cropRect = const Rect.fromLTWH(0.1, 0.1, 0.8, 0.8);
  bool _isDragging = false;
  String _dragHandle = '';

  void resetCrop() {
    setState(() {
      _cropRect = const Rect.fromLTWH(0.1, 0.1, 0.8, 0.8);
    });
    widget.onCropChanged(_cropRect);
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            // Main image
            Positioned.fill(
              child: Transform.rotate(
                angle: widget.rotation * (math.pi / 180),
                child: Transform.scale(
                  scaleX: widget.flipHorizontal ? -1 : 1,
                  scaleY: widget.flipVertical ? -1 : 1,
                  child: Image.memory(
                    widget.imageData,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
            
            // Crop overlay
            Positioned.fill(
              child: CustomPaint(
                painter: CropOverlayPainter(_cropRect),
              ),
            ),
            
            // Crop handles
            ..._buildCropHandles(constraints),
          ],
        );
      },
    );
  }

  List<Widget> _buildCropHandles(BoxConstraints constraints) {
    final width = constraints.maxWidth;
    final height = constraints.maxHeight;
    
    final left = _cropRect.left * width;
    final top = _cropRect.top * height;
    final right = _cropRect.right * width;
    final bottom = _cropRect.bottom * height;

    return [
      // Top-left handle
      _buildHandle(
        left - 10,
        top - 10,
        'top-left',
        Icons.crop_free,
      ),
      
      // Top-right handle
      _buildHandle(
        right - 10,
        top - 10,
        'top-right',
        Icons.crop_free,
      ),
      
      // Bottom-left handle
      _buildHandle(
        left - 10,
        bottom - 10,
        'bottom-left',
        Icons.crop_free,
      ),
      
      // Bottom-right handle
      _buildHandle(
        right - 10,
        bottom - 10,
        'bottom-right',
        Icons.crop_free,
      ),
      
      // Center drag area
      Positioned(
        left: left,
        top: top,
        width: right - left,
        height: bottom - top,
        child: GestureDetector(
          onPanStart: (details) {
            setState(() {
              _isDragging = true;
              _dragHandle = 'center';
            });
            HapticFeedback.lightImpact();
          },
          onPanUpdate: (details) {
            if (_dragHandle == 'center') {
              _moveCropRect(details.delta, constraints);
            }
          },
          onPanEnd: (details) {
            setState(() {
              _isDragging = false;
              _dragHandle = '';
            });
          },
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white.withOpacity(0.8),
                width: 2,
              ),
            ),
            child: CustomPaint(
              painter: GridPainter(),
            ),
          ),
        ),
      ),
    ];
  }

  Widget _buildHandle(double left, double top, String handle, IconData icon) {
    return Positioned(
      left: left,
      top: top,
      child: GestureDetector(
        onPanStart: (details) {
          setState(() {
            _isDragging = true;
            _dragHandle = handle;
          });
          HapticFeedback.mediumImpact();
        },
        onPanUpdate: (details) {
          _resizeCropRect(details.delta, handle, context);
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
            _dragHandle = '';
          });
          widget.onCropChanged(_cropRect);
        },
        child: Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.blue,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            icon,
            size: 12,
            color: Colors.blue,
          ),
        ),
      ),
    );
  }

  void _moveCropRect(Offset delta, BoxConstraints constraints) {
    final width = constraints.maxWidth;
    final height = constraints.maxHeight;
    
    final deltaX = delta.dx / width;
    final deltaY = delta.dy / height;
    
    setState(() {
      final newLeft = (_cropRect.left + deltaX).clamp(0.0, 1.0 - _cropRect.width);
      final newTop = (_cropRect.top + deltaY).clamp(0.0, 1.0 - _cropRect.height);
      
      _cropRect = Rect.fromLTWH(
        newLeft,
        newTop,
        _cropRect.width,
        _cropRect.height,
      );
    });
  }

  void _resizeCropRect(Offset delta, String handle, BuildContext context) {
    final size = MediaQuery.of(context).size;
    final deltaX = delta.dx / size.width;
    final deltaY = delta.dy / size.height;
    
    setState(() {
      double left = _cropRect.left;
      double top = _cropRect.top;
      double right = _cropRect.right;
      double bottom = _cropRect.bottom;
      
      switch (handle) {
        case 'top-left':
          left = (left + deltaX).clamp(0.0, right - 0.1);
          top = (top + deltaY).clamp(0.0, bottom - 0.1);
          break;
        case 'top-right':
          right = (right + deltaX).clamp(left + 0.1, 1.0);
          top = (top + deltaY).clamp(0.0, bottom - 0.1);
          break;
        case 'bottom-left':
          left = (left + deltaX).clamp(0.0, right - 0.1);
          bottom = (bottom + deltaY).clamp(top + 0.1, 1.0);
          break;
        case 'bottom-right':
          right = (right + deltaX).clamp(left + 0.1, 1.0);
          bottom = (bottom + deltaY).clamp(top + 0.1, 1.0);
          break;
      }
      
      _cropRect = Rect.fromLTRB(left, top, right, bottom);
    });
  }


}

class CropOverlayPainter extends CustomPainter {
  final Rect cropRect;

  CropOverlayPainter(this.cropRect);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    final cropLeft = cropRect.left * size.width;
    final cropTop = cropRect.top * size.height;
    final cropRight = cropRect.right * size.width;
    final cropBottom = cropRect.bottom * size.height;

    // Draw overlay outside crop area
    // Top
    canvas.drawRect(
      Rect.fromLTRB(0, 0, size.width, cropTop),
      paint,
    );
    
    // Bottom
    canvas.drawRect(
      Rect.fromLTRB(0, cropBottom, size.width, size.height),
      paint,
    );
    
    // Left
    canvas.drawRect(
      Rect.fromLTRB(0, cropTop, cropLeft, cropBottom),
      paint,
    );
    
    // Right
    canvas.drawRect(
      Rect.fromLTRB(cropRight, cropTop, size.width, cropBottom),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.5)
      ..strokeWidth = 1;

    // Vertical lines
    canvas.drawLine(
      Offset(size.width / 3, 0),
      Offset(size.width / 3, size.height),
      paint,
    );
    canvas.drawLine(
      Offset(size.width * 2 / 3, 0),
      Offset(size.width * 2 / 3, size.height),
      paint,
    );

    // Horizontal lines
    canvas.drawLine(
      Offset(0, size.height / 3),
      Offset(size.width, size.height / 3),
      paint,
    );
    canvas.drawLine(
      Offset(0, size.height * 2 / 3),
      Offset(size.width, size.height * 2 / 3),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
