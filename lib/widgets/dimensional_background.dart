import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui';
import '../design_system/nexus_theme.dart';

/// Dimensional Background - Living cosmic environment
/// Features floating particles, energy fields, and dimensional rifts
class DimensionalBackground extends StatefulWidget {
  final double rotation;
  final double intensity;

  const DimensionalBackground({
    super.key,
    required this.rotation,
    required this.intensity,
  });

  @override
  State<DimensionalBackground> createState() => _DimensionalBackgroundState();
}

class _DimensionalBackgroundState extends State<DimensionalBackground>
    with TickerProviderStateMixin {
  late AnimationController _particleController;
  late AnimationController _waveController;
  late AnimationController _riftController;
  
  late Animation<double> _particleFlow;
  late Animation<double> _waveMotion;
  late Animation<double> _riftPulse;
  
  final List<CosmicParticle> _particles = [];
  final List<EnergyWave> _waves = [];
  final List<DimensionalRift> _rifts = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generateCosmicElements();
  }

  void _initializeAnimations() {
    _particleController = AnimationController(
      duration: NexusAnimations.infinite,
      vsync: this,
    );
    _particleFlow = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );
    _particleController.repeat();

    _waveController = AnimationController(
      duration: NexusAnimations.cosmic,
      vsync: this,
    );
    _waveMotion = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.linear),
    );
    _waveController.repeat();

    _riftController = AnimationController(
      duration: NexusAnimations.universal,
      vsync: this,
    );
    _riftPulse = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _riftController, curve: Curves.easeInOut),
    );
    _riftController.repeat(reverse: true);
  }

  void _generateCosmicElements() {
    final random = math.Random();
    
    // Generate floating particles
    for (int i = 0; i < 50; i++) {
      _particles.add(CosmicParticle(
        position: Offset(
          random.nextDouble(),
          random.nextDouble(),
        ),
        velocity: Offset(
          (random.nextDouble() - 0.5) * 0.002,
          (random.nextDouble() - 0.5) * 0.002,
        ),
        size: random.nextDouble() * 3 + 1,
        color: _getRandomCosmicColor(random),
        energy: random.nextDouble(),
        phase: random.nextDouble() * 2 * math.pi,
      ));
    }

    // Generate energy waves
    for (int i = 0; i < 8; i++) {
      _waves.add(EnergyWave(
        center: Offset(
          random.nextDouble(),
          random.nextDouble(),
        ),
        radius: random.nextDouble() * 200 + 100,
        frequency: random.nextDouble() * 0.02 + 0.01,
        amplitude: random.nextDouble() * 50 + 20,
        color: _getRandomCosmicColor(random),
        phase: random.nextDouble() * 2 * math.pi,
      ));
    }

    // Generate dimensional rifts
    for (int i = 0; i < 3; i++) {
      _rifts.add(DimensionalRift(
        start: Offset(
          random.nextDouble(),
          random.nextDouble(),
        ),
        end: Offset(
          random.nextDouble(),
          random.nextDouble(),
        ),
        width: random.nextDouble() * 5 + 2,
        energy: random.nextDouble(),
        color: _getRandomCosmicColor(random),
        distortion: random.nextDouble() * 0.1 + 0.05,
      ));
    }
  }

  Color _getRandomCosmicColor(math.Random random) {
    final colors = [
      NexusTheme.cosmicPurple,
      NexusTheme.electricBlue,
      NexusTheme.neonGreen,
      NexusTheme.magentaFlare,
      NexusTheme.aquaMist,
      NexusTheme.goldenAura,
    ];
    return colors[random.nextInt(colors.length)];
  }

  @override
  void dispose() {
    _particleController.dispose();
    _waveController.dispose();
    _riftController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _particleController,
        _waveController,
        _riftController,
      ]),
      builder: (context, child) {
        return CustomPaint(
          painter: DimensionalPainter(
            particles: _particles,
            waves: _waves,
            rifts: _rifts,
            particleFlow: _particleFlow.value,
            waveMotion: _waveMotion.value,
            riftPulse: _riftPulse.value,
            rotation: widget.rotation,
            intensity: widget.intensity,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

/// Custom painter for the dimensional background
class DimensionalPainter extends CustomPainter {
  final List<CosmicParticle> particles;
  final List<EnergyWave> waves;
  final List<DimensionalRift> rifts;
  final double particleFlow;
  final double waveMotion;
  final double riftPulse;
  final double rotation;
  final double intensity;

  DimensionalPainter({
    required this.particles,
    required this.waves,
    required this.rifts,
    required this.particleFlow,
    required this.waveMotion,
    required this.riftPulse,
    required this.rotation,
    required this.intensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Apply cosmic transformation
    canvas.save();
    canvas.translate(size.width / 2, size.height / 2);
    canvas.rotate(rotation * 0.1);
    canvas.translate(-size.width / 2, -size.height / 2);

    // Paint dimensional rifts
    _paintDimensionalRifts(canvas, size);
    
    // Paint energy waves
    _paintEnergyWaves(canvas, size);
    
    // Paint cosmic particles
    _paintCosmicParticles(canvas, size);
    
    // Paint neural connections
    _paintNeuralConnections(canvas, size);

    canvas.restore();
  }

  void _paintDimensionalRifts(Canvas canvas, Size size) {
    for (final rift in rifts) {
      final paint = Paint()
        ..color = rift.color.withOpacity(0.3 * riftPulse * intensity)
        ..strokeWidth = rift.width * intensity
        ..style = PaintingStyle.stroke
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 10 * intensity);

      final start = Offset(
        rift.start.dx * size.width,
        rift.start.dy * size.height,
      );
      final end = Offset(
        rift.end.dx * size.width,
        rift.end.dy * size.height,
      );

      // Create distorted path
      final path = Path();
      path.moveTo(start.dx, start.dy);
      
      final controlPoint1 = Offset(
        start.dx + (end.dx - start.dx) * 0.3 + 
        math.sin(waveMotion + rift.energy * 2 * math.pi) * 50 * rift.distortion,
        start.dy + (end.dy - start.dy) * 0.3,
      );
      
      final controlPoint2 = Offset(
        start.dx + (end.dx - start.dx) * 0.7 + 
        math.cos(waveMotion + rift.energy * 2 * math.pi) * 50 * rift.distortion,
        start.dy + (end.dy - start.dy) * 0.7,
      );

      path.cubicTo(
        controlPoint1.dx, controlPoint1.dy,
        controlPoint2.dx, controlPoint2.dy,
        end.dx, end.dy,
      );

      canvas.drawPath(path, paint);
    }
  }

  void _paintEnergyWaves(Canvas canvas, Size size) {
    for (final wave in waves) {
      final center = Offset(
        wave.center.dx * size.width,
        wave.center.dy * size.height,
      );

      final paint = Paint()
        ..color = wave.color.withOpacity(0.2 * intensity)
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;

      // Create ripple effect
      for (int i = 0; i < 3; i++) {
        final radius = wave.radius + 
                      math.sin(waveMotion * wave.frequency + wave.phase + i) * 
                      wave.amplitude;
        
        if (radius > 0) {
          canvas.drawCircle(center, radius, paint);
        }
      }
    }
  }

  void _paintCosmicParticles(Canvas canvas, Size size) {
    for (final particle in particles) {
      // Update particle position
      final currentPos = Offset(
        (particle.position.dx + particle.velocity.dx * particleFlow) % 1.0,
        (particle.position.dy + particle.velocity.dy * particleFlow) % 1.0,
      );

      final position = Offset(
        currentPos.dx * size.width,
        currentPos.dy * size.height,
      );

      // Pulsing effect
      final pulseSize = particle.size * 
                       (1.0 + math.sin(waveMotion + particle.phase) * 0.3) *
                       intensity;

      final paint = Paint()
        ..color = particle.color.withOpacity(particle.energy * 0.8)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, pulseSize);

      canvas.drawCircle(position, pulseSize, paint);
    }
  }

  void _paintNeuralConnections(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = NexusTheme.electricBlue.withOpacity(0.1 * intensity)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Connect nearby particles with neural lines
    for (int i = 0; i < particles.length; i++) {
      for (int j = i + 1; j < particles.length; j++) {
        final pos1 = Offset(
          particles[i].position.dx * size.width,
          particles[i].position.dy * size.height,
        );
        final pos2 = Offset(
          particles[j].position.dx * size.width,
          particles[j].position.dy * size.height,
        );

        final distance = (pos1 - pos2).distance;
        if (distance < 150) {
          final opacity = (1.0 - distance / 150) * 0.3 * intensity;
          paint.color = NexusTheme.electricBlue.withOpacity(opacity);
          canvas.drawLine(pos1, pos2, paint);
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Data models for cosmic elements
class CosmicParticle {
  final Offset position;
  final Offset velocity;
  final double size;
  final Color color;
  final double energy;
  final double phase;

  const CosmicParticle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.color,
    required this.energy,
    required this.phase,
  });
}

class EnergyWave {
  final Offset center;
  final double radius;
  final double frequency;
  final double amplitude;
  final Color color;
  final double phase;

  const EnergyWave({
    required this.center,
    required this.radius,
    required this.frequency,
    required this.amplitude,
    required this.color,
    required this.phase,
  });
}

class DimensionalRift {
  final Offset start;
  final Offset end;
  final double width;
  final double energy;
  final Color color;
  final double distortion;

  const DimensionalRift({
    required this.start,
    required this.end,
    required this.width,
    required this.energy,
    required this.color,
    required this.distortion,
  });
}
