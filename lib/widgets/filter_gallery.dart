import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';
import '../providers/theme_provider.dart';

class FilterGallery extends StatefulWidget {
  final Function(String) onFilterSelected;
  final Uint8List? originalImage;

  const FilterGallery({
    super.key,
    required this.onFilterSelected,
    this.originalImage,
  });

  @override
  State<FilterGallery> createState() => _FilterGalleryState();
}

class _FilterGalleryState extends State<FilterGallery> {
  String? _selectedFilter;
  
  final List<FilterOption> _filters = [
    FilterOption('Original', Icons.image_outlined, null),
    FilterOption('Vintage', Icons.camera_alt_outlined, Colors.amber),
    FilterOption('B&W', Icons.filter_b_and_w_outlined, Colors.grey),
    FilterOption('Sepia', Icons.palette_outlined, Colors.brown),
    FilterOption('Cool', Icons.ac_unit_outlined, Colors.blue),
    FilterOption('Warm', Icons.wb_sunny_outlined, Colors.orange),
    FilterOption('Dramatic', Icons.flash_on_outlined, Colors.red),
    FilterOption('Soft', Icons.blur_on_outlined, Colors.pink),
    FilterOption('Vivid', Icons.nature_outlined, Colors.green),
    FilterOption('Moody', Icons.dark_mode_outlined, Colors.purple),
    FilterOption('Film', Icons.movie_outlined, Colors.indigo),
    FilterOption('Retro', Icons.photo_camera_outlined, Colors.teal),
    FilterOption('Neon', Icons.highlight_outlined, Colors.cyan),
    FilterOption('Pastel', Icons.color_lens_outlined, Colors.lightBlue),
    FilterOption('Pop Art', Icons.art_track_outlined, Colors.deepOrange),
    FilterOption('Cinema', Icons.movie_filter_outlined, Colors.deepPurple),
    FilterOption('Golden', Icons.wb_incandescent_outlined, Colors.yellow),
    FilterOption('Forest', Icons.forest_outlined, Colors.lightGreen),
    FilterOption('Portrait', Icons.face_outlined, Colors.pinkAccent),
    FilterOption('Glow', Icons.lightbulb_outline, Colors.yellowAccent),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 140,
      child: Column(
        children: [
          // Filter categories
          Container(
            height: 40,
            child: ListView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              children: [
                _buildFilterCategory('All', true),
                _buildFilterCategory('Vintage', false),
                _buildFilterCategory('Artistic', false),
                _buildFilterCategory('Portrait', false),
                _buildFilterCategory('Nature', false),
              ],
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Filter options
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: _filters.length,
              itemBuilder: (context, index) {
                final filter = _filters[index];
                final isSelected = _selectedFilter == filter.name;
                
                return GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _selectedFilter = filter.name;
                    });
                    widget.onFilterSelected(filter.name);
                  },
                  child: Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 16),
                    child: Column(
                      children: [
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            gradient: isSelected ? LinearGradient(
                              colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                            ) : null,
                            color: isSelected ? null : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(18),
                            border: Border.all(
                              color: isSelected 
                                ? Colors.transparent
                                : Colors.white.withOpacity(0.3),
                              width: isSelected ? 0 : 1,
                            ),
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: AppColors.lightPrimary.withOpacity(0.4),
                                blurRadius: 12,
                                offset: const Offset(0, 6),
                              ),
                            ] : null,
                          ),
                          child: Stack(
                            children: [
                              // Filter preview
                              if (widget.originalImage != null)
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(18),
                                  child: ColorFiltered(
                                    colorFilter: _getColorFilter(filter),
                                    child: Image.memory(
                                      widget.originalImage!,
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              
                              // Filter overlay
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(18),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.transparent,
                                      Colors.black.withOpacity(0.6),
                                    ],
                                  ),
                                ),
                              ),
                              
                              // Filter icon
                              Center(
                                child: Icon(
                                  filter.icon,
                                  color: Colors.white,
                                  size: isSelected ? 24 : 20,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        Text(
                          filter.name,
                          style: TextStyle(
                            color: isSelected ? AppColors.lightPrimary : Colors.white,
                            fontSize: 11,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterCategory(String name, bool isSelected) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // TODO: Filter by category
      },
      child: Container(
        margin: const EdgeInsets.only(right: 12),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected ? LinearGradient(
            colors: [AppColors.lightPrimary, AppColors.lightSecondary],
          ) : null,
          color: isSelected ? null : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
              ? Colors.transparent
              : Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: AppColors.lightPrimary.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Text(
          name,
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }

  ColorFilter _getColorFilter(FilterOption filter) {
    switch (filter.name) {
      case 'Original':
        return const ColorFilter.mode(Colors.transparent, BlendMode.multiply);
      case 'B&W':
        return const ColorFilter.mode(Colors.grey, BlendMode.saturation);
      case 'Sepia':
        return ColorFilter.mode(Colors.brown.withOpacity(0.3), BlendMode.overlay);
      case 'Cool':
        return ColorFilter.mode(Colors.blue.withOpacity(0.2), BlendMode.overlay);
      case 'Warm':
        return ColorFilter.mode(Colors.orange.withOpacity(0.2), BlendMode.overlay);
      case 'Vintage':
        return ColorFilter.mode(Colors.amber.withOpacity(0.3), BlendMode.overlay);
      case 'Dramatic':
        return const ColorFilter.matrix([
          1.2, 0, 0, 0, 0,
          0, 1.2, 0, 0, 0,
          0, 0, 1.2, 0, 0,
          0, 0, 0, 1, 0,
        ]);
      case 'Soft':
        return const ColorFilter.matrix([
          0.8, 0, 0, 0, 50,
          0, 0.8, 0, 0, 50,
          0, 0, 0.8, 0, 50,
          0, 0, 0, 1, 0,
        ]);
      case 'Vivid':
        return const ColorFilter.matrix([
          1.4, 0, 0, 0, 0,
          0, 1.4, 0, 0, 0,
          0, 0, 1.4, 0, 0,
          0, 0, 0, 1, 0,
        ]);
      default:
        return ColorFilter.mode(
          filter.color?.withOpacity(0.2) ?? Colors.transparent, 
          BlendMode.overlay,
        );
    }
  }
}

class FilterOption {
  final String name;
  final IconData icon;
  final Color? color;

  const FilterOption(this.name, this.icon, this.color);
}
