import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../providers/theme_provider.dart';

class LoadingOverlay extends StatefulWidget {
  final bool isVisible;
  final String text;
  final Animation<double> animation;

  const LoadingOverlay({
    super.key,
    required this.isVisible,
    required this.text,
    required this.animation,
  });

  @override
  State<LoadingOverlay> createState() => _LoadingOverlayState();
}

class _LoadingOverlayState extends State<LoadingOverlay>
    with TickerProviderStateMixin {
  late AnimationController _overlayController;
  late AnimationController _particleController;
  late Animation<double> _overlayAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _overlayController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _overlayAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _overlayController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _overlayController,
      curve: Curves.elasticOut,
    ));

    if (widget.isVisible) {
      _overlayController.forward();
      _particleController.repeat();
    }
  }

  @override
  void didUpdateWidget(LoadingOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _overlayController.forward();
        _particleController.repeat();
      } else {
        _overlayController.reverse();
        _particleController.stop();
      }
    }
  }

  @override
  void dispose() {
    _overlayController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _overlayAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _overlayAnimation.value,
          child: Container(
            color: Colors.black.withOpacity(0.8),
            child: Center(
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Container(
                  padding: const EdgeInsets.all(32),
                  margin: const EdgeInsets.symmetric(horizontal: 40),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(0.15),
                        Colors.white.withOpacity(0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          // Particle effect background
                          SizedBox(
                            width: 120,
                            height: 120,
                            child: AnimatedBuilder(
                              animation: _particleController,
                              builder: (context, child) {
                                return CustomPaint(
                                  painter: ParticlesPainter(_particleController.value),
                                  size: const Size(120, 120),
                                );
                              },
                            ),
                          ),
                          // Main loading indicator
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.lightPrimary,
                                  AppColors.lightSecondary,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(40),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.lightPrimary.withOpacity(0.4),
                                  blurRadius: 20,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: AnimatedBuilder(
                              animation: widget.animation,
                              builder: (context, child) {
                                return Transform.rotate(
                                  angle: widget.animation.value * 2 * math.pi,
                                  child: const Icon(
                                    Icons.auto_fix_high_rounded,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                );
                              },
                            ),
                          ),
                          // Outer ring
                          SizedBox(
                            width: 100,
                            height: 100,
                            child: AnimatedBuilder(
                              animation: widget.animation,
                              builder: (context, child) {
                                return CircularProgressIndicator(
                                  value: null,
                                  strokeWidth: 3,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.lightPrimary.withOpacity(0.6),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Text(
                        widget.text,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Please wait...',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class ParticlesPainter extends CustomPainter {
  final double animationValue;
  final List<Particle> particles;

  ParticlesPainter(this.animationValue) : particles = _generateParticles();

  static List<Particle> _generateParticles() {
    final particles = <Particle>[];
    for (int i = 0; i < 20; i++) {
      particles.add(Particle(
        angle: (i * 18.0) * (math.pi / 180),
        radius: 30.0 + (i % 3) * 10.0,
        speed: 0.5 + (i % 3) * 0.3,
        size: 2.0 + (i % 3),
      ));
    }
    return particles;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    for (final particle in particles) {
      final progress = (animationValue * particle.speed) % 1.0;
      final currentRadius = particle.radius * (1.0 + progress * 0.5);
      final opacity = (1.0 - progress) * 0.8;
      
      final x = center.dx + currentRadius * math.cos(particle.angle + animationValue * 2 * math.pi);
      final y = center.dy + currentRadius * math.sin(particle.angle + animationValue * 2 * math.pi);
      
      final paint = Paint()
        ..color = AppColors.lightPrimary.withOpacity(opacity)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(x, y),
        particle.size * (1.0 - progress * 0.5),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class Particle {
  final double angle;
  final double radius;
  final double speed;
  final double size;

  Particle({
    required this.angle,
    required this.radius,
    required this.speed,
    required this.size,
  });
}

class WaveLoadingIndicator extends StatefulWidget {
  final Color color;
  final double size;

  const WaveLoadingIndicator({
    super.key,
    this.color = Colors.blue,
    this.size = 50.0,
  });

  @override
  State<WaveLoadingIndicator> createState() => _WaveLoadingIndicatorState();
}

class _WaveLoadingIndicatorState extends State<WaveLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return CustomPaint(
            painter: WavePainter(
              animationValue: _controller.value,
              color: widget.color,
            ),
          );
        },
      ),
    );
  }
}

class WavePainter extends CustomPainter {
  final double animationValue;
  final Color color;

  WavePainter({
    required this.animationValue,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = size.width / 2;

    for (int i = 0; i < 3; i++) {
      final progress = (animationValue + i * 0.3) % 1.0;
      final radius = maxRadius * progress;
      final opacity = (1.0 - progress) * 0.6;

      final paint = Paint()
        ..color = color.withOpacity(opacity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
