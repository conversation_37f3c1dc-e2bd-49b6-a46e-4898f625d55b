import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../providers/theme_provider.dart';

class ManualAdjustments extends StatefulWidget {
  final double brightness;
  final double contrast;
  final double saturation;
  final double highlights;
  final double shadows;
  final double warmth;
  final double tint;
  final double clarity;
  final double vibrance;
  final double exposure;
  final double gamma;
  final double hue;
  final Function(Map<String, double>) onChanged;

  const ManualAdjustments({
    super.key,
    required this.brightness,
    required this.contrast,
    required this.saturation,
    required this.highlights,
    required this.shadows,
    required this.warmth,
    required this.tint,
    required this.clarity,
    required this.vibrance,
    required this.exposure,
    required this.gamma,
    required this.hue,
    required this.onChanged,
  });

  @override
  State<ManualAdjustments> createState() => _ManualAdjustmentsState();
}

class _ManualAdjustmentsState extends State<ManualAdjustments> {
  String? _selectedAdjustment;

  final List<AdjustmentControl> _adjustments = [
    AdjustmentControl(
      name: 'Brightness',
      icon: Icons.brightness_6_rounded,
      min: -1.0,
      max: 1.0,
      key: 'brightness',
    ),
    AdjustmentControl(
      name: 'Contrast',
      icon: Icons.contrast_rounded,
      min: -1.0,
      max: 1.0,
      key: 'contrast',
    ),
    AdjustmentControl(
      name: 'Saturation',
      icon: Icons.palette_rounded,
      min: -1.0,
      max: 1.0,
      key: 'saturation',
    ),
    AdjustmentControl(
      name: 'Highlights',
      icon: Icons.highlight_rounded,
      min: -1.0,
      max: 1.0,
      key: 'highlights',
    ),
    AdjustmentControl(
      name: 'Shadows',
      icon: Icons.brightness_2_rounded,
      min: -1.0,
      max: 1.0,
      key: 'shadows',
    ),
    AdjustmentControl(
      name: 'Warmth',
      icon: Icons.wb_sunny_rounded,
      min: -1.0,
      max: 1.0,
      key: 'warmth',
    ),
    AdjustmentControl(
      name: 'Tint',
      icon: Icons.color_lens_rounded,
      min: -1.0,
      max: 1.0,
      key: 'tint',
    ),
    AdjustmentControl(
      name: 'Clarity',
      icon: Icons.hd_rounded,
      min: -1.0,
      max: 1.0,
      key: 'clarity',
    ),
    AdjustmentControl(
      name: 'Vibrance',
      icon: Icons.vibration_rounded,
      min: -1.0,
      max: 1.0,
      key: 'vibrance',
    ),
    AdjustmentControl(
      name: 'Exposure',
      icon: Icons.exposure_rounded,
      min: -2.0,
      max: 2.0,
      key: 'exposure',
    ),
    AdjustmentControl(
      name: 'Gamma',
      icon: Icons.settings_rounded,
      min: 0.1,
      max: 3.0,
      key: 'gamma',
    ),
    AdjustmentControl(
      name: 'Hue',
      icon: Icons.tune_rounded,
      min: -180.0,
      max: 180.0,
      key: 'hue',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 160,
      child: Column(
        children: [
          // Adjustment controls grid
          Container(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: _adjustments.length,
              itemBuilder: (context, index) {
                final adjustment = _adjustments[index];
                final isSelected = _selectedAdjustment == adjustment.key;
                final currentValue = _getCurrentValue(adjustment.key);
                
                return GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _selectedAdjustment = isSelected ? null : adjustment.key;
                    });
                  },
                  child: Container(
                    width: 70,
                    margin: const EdgeInsets.only(right: 12),
                    child: Column(
                      children: [
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: isSelected ? LinearGradient(
                              colors: [AppColors.lightPrimary, AppColors.lightSecondary],
                            ) : null,
                            color: isSelected ? null : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: isSelected 
                                ? Colors.transparent
                                : Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                            boxShadow: isSelected ? [
                              BoxShadow(
                                color: AppColors.lightPrimary.withOpacity(0.4),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                              ),
                            ] : null,
                          ),
                          child: Icon(
                            adjustment.icon,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          adjustment.name,
                          style: TextStyle(
                            color: isSelected ? AppColors.lightPrimary : Colors.white,
                            fontSize: 11,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Slider for selected adjustment
          if (_selectedAdjustment != null) _buildSlider(),
        ],
      ),
    );
  }

  Widget _buildSlider() {
    final adjustment = _adjustments.firstWhere(
      (adj) => adj.key == _selectedAdjustment,
    );
    final currentValue = _getCurrentValue(adjustment.key);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                adjustment.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.lightPrimary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.lightPrimary.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  currentValue.toStringAsFixed(2),
                  style: TextStyle(
                    color: AppColors.lightPrimary,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _updateValue(adjustment.key, adjustment.min);
                },
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.remove_rounded,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: AppColors.lightPrimary,
                      inactiveTrackColor: Colors.white.withOpacity(0.3),
                      thumbColor: AppColors.lightPrimary,
                      overlayColor: AppColors.lightPrimary.withOpacity(0.2),
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 12,
                      ),
                      overlayShape: const RoundSliderOverlayShape(
                        overlayRadius: 20,
                      ),
                      trackHeight: 4,
                    ),
                    child: Slider(
                      value: currentValue,
                      min: adjustment.min,
                      max: adjustment.max,
                      onChanged: (value) {
                        _updateValue(adjustment.key, value);
                      },
                    ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _updateValue(adjustment.key, adjustment.max);
                },
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.add_rounded,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              _updateValue(adjustment.key, 0.0);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Text(
                'Reset',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getCurrentValue(String key) {
    switch (key) {
      case 'brightness':
        return widget.brightness;
      case 'contrast':
        return widget.contrast;
      case 'saturation':
        return widget.saturation;
      case 'highlights':
        return widget.highlights;
      case 'shadows':
        return widget.shadows;
      case 'warmth':
        return widget.warmth;
      case 'tint':
        return widget.tint;
      case 'clarity':
        return widget.clarity;
      case 'vibrance':
        return widget.vibrance;
      case 'exposure':
        return widget.exposure;
      case 'gamma':
        return widget.gamma;
      case 'hue':
        return widget.hue;
      default:
        return 0.0;
    }
  }

  void _updateValue(String key, double value) {
    final adjustments = <String, double>{
      'brightness': widget.brightness,
      'contrast': widget.contrast,
      'saturation': widget.saturation,
      'highlights': widget.highlights,
      'shadows': widget.shadows,
      'warmth': widget.warmth,
      'tint': widget.tint,
      'clarity': widget.clarity,
      'vibrance': widget.vibrance,
      'exposure': widget.exposure,
      'gamma': widget.gamma,
      'hue': widget.hue,
    };
    
    adjustments[key] = value;
    widget.onChanged(adjustments);
  }
}

class AdjustmentControl {
  final String name;
  final IconData icon;
  final double min;
  final double max;
  final String key;

  const AdjustmentControl({
    required this.name,
    required this.icon,
    required this.min,
    required this.max,
    required this.key,
  });
}
