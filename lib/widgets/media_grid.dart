import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_manager/photo_manager.dart';

import '../models/media_item.dart';
import '../utils/constants.dart';

class MediaGrid extends StatelessWidget {
  final List<MediaItem> media;
  final int gridColumns;
  final Set<String> selectedItems;
  final bool isSelectionMode;
  final Function(MediaItem) onItemTap;
  final Function(MediaItem) onItemLongPress;
  final Function(MediaItem) onItemSelected;

  const MediaGrid({
    super.key,
    required this.media,
    required this.gridColumns,
    required this.selectedItems,
    required this.isSelectionMode,
    required this.onItemTap,
    required this.onItemLongPress,
    required this.onItemSelected,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultGridSpacing),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: gridColumns,
        crossAxisSpacing: AppConstants.defaultGridSpacing,
        mainAxisSpacing: AppConstants.defaultGridSpacing,
        childAspectRatio: AppConstants.defaultGridChildAspectRatio,
      ),
      itemCount: media.length,
      itemBuilder: (context, index) {
        final item = media[index];
        final isSelected = selectedItems.contains(item.id);
        
        return MediaItemWidget(
          mediaItem: item,
          isSelected: isSelected,
          isSelectionMode: isSelectionMode,
          onTap: () => onItemTap(item),
          onLongPress: () => onItemLongPress(item),
          onSelectionChanged: () => onItemSelected(item),
        );
      },
    );
  }
}

class MediaItemWidget extends StatelessWidget {
  final MediaItem mediaItem;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final VoidCallback onSelectionChanged;

  const MediaItemWidget({
    super.key,
    required this.mediaItem,
    required this.isSelected,
    required this.isSelectionMode,
    required this.onTap,
    required this.onLongPress,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(
                  color: AppConstants.primaryColor,
                  width: 3,
                )
              : null,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // Media thumbnail
              _buildThumbnail(),
              
              // Selection overlay
              if (isSelectionMode)
                Positioned.fill(
                  child: Container(
                    color: isSelected
                        ? AppConstants.primaryColor.withOpacity(0.3)
                        : Colors.black.withOpacity(0.1),
                  ),
                ),
              
              // Selection checkbox
              if (isSelectionMode)
                Positioned(
                  top: 8,
                  right: 8,
                  child: GestureDetector(
                    onTap: onSelectionChanged,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppConstants.primaryColor
                            : Colors.white.withOpacity(0.8),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? AppConstants.primaryColor
                              : Colors.grey,
                          width: 2,
                        ),
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              size: 16,
                              color: Colors.white,
                            )
                          : null,
                    ),
                  ),
                ),
              
              // Video indicator
              if (mediaItem.type == MediaType.video)
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.play_arrow,
                          size: 12,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          _formatDuration(mediaItem.duration),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              
              // Favorite indicator
              if (mediaItem.isFavorite)
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.favorite,
                      size: 12,
                      color: Colors.red,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThumbnail() {
    if (mediaItem.assetEntity != null) {
      // return (
      //   mediaItem.assetEntity!,
      //   isOriginal: false,
      //   thumbnailSize: const ThumbnailSize.square(300),
      //   fit: BoxFit.cover,
      //   errorBuilder: (context, error, stackTrace) {
      //     return Container(
      //       color: Colors.grey[300],
      //       child: const Icon(
      //         Icons.broken_image,
      //         color: Colors.grey,
      //       ),
      //     );
      //   },
      // );
      return Container();
    } else {
      return Container(
        color: Colors.grey[300],
        child: const Icon(
          Icons.image,
          color: Colors.grey,
        ),
      );
    }
  }

  String _formatDuration(int seconds) {
    if (seconds < 60) {
      return '0:${seconds.toString().padLeft(2, '0')}';
    } else {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return '${minutes}:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }
} 