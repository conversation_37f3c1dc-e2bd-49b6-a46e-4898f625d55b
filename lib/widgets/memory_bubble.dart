import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui';
import '../design_system/nexus_theme.dart';
import '../screens/nexus_home_screen.dart';

/// Revolutionary Memory Bubble - 3D floating memory container
/// Each bubble represents an AI-clustered collection of memories
class MemoryBubble extends StatefulWidget {
  final MemoryCluster cluster;
  final bool isActive;
  final double pulseIntensity;

  const MemoryBubble({
    super.key,
    required this.cluster,
    required this.isActive,
    required this.pulseIntensity,
  });

  @override
  State<MemoryBubble> createState() => _MemoryBubbleState();
}

class _MemoryBubbleState extends State<MemoryBubble>
    with TickerProviderStateMixin {
  late AnimationController _breathingController;
  late AnimationController _rotationController;
  late AnimationController _energyController;
  
  late Animation<double> _breathingScale;
  late Animation<double> _rotation;
  late Animation<double> _energyPulse;
  
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Breathing animation - makes bubble feel alive
    _breathingController = AnimationController(
      duration: Duration(
        milliseconds: (2000 + (widget.cluster.energy * 1000)).round(),
      ),
      vsync: this,
    );
    _breathingScale = NexusAnimations.breathingScale(_breathingController);
    _breathingController.repeat(reverse: true);

    // Rotation for 3D effect
    _rotationController = AnimationController(
      duration: Duration(
        milliseconds: (5000 + (widget.cluster.energy * 2000)).round(),
      ),
      vsync: this,
    );
    _rotation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );
    _rotationController.repeat();

    // Energy pulse based on cluster energy
    _energyController = AnimationController(
      duration: Duration(
        milliseconds: (1000 + (widget.cluster.energy * 500)).round(),
      ),
      vsync: this,
    );
    _energyPulse = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(parent: _energyController, curve: Curves.easeInOut),
    );
    _energyController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _breathingController.dispose();
    _rotationController.dispose();
    _energyController.dispose();
    super.dispose();
  }

  Color _getEmotionColor() {
    switch (widget.cluster.emotion) {
      case EmotionType.joy:
        return NexusTheme.neonGreen;
      case EmotionType.love:
        return NexusTheme.magentaFlare;
      case EmotionType.mystery:
        return NexusTheme.cosmicPurple;
      case EmotionType.energy:
        return NexusTheme.electricBlue;
      case EmotionType.peace:
        return NexusTheme.aquaMist;
      case EmotionType.nostalgia:
        return NexusTheme.goldenAura;
      case EmotionType.adventure:
        return NexusTheme.sunsetOrange;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _breathingController,
          _rotationController,
          _energyController,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _breathingScale.value * 
                   (widget.isActive ? 1.2 : 1.0) * 
                   (_isHovered ? 1.1 : 1.0),
            child: Transform.rotate(
              angle: _rotation.value * 0.1, // Subtle rotation
              child: Container(
                width: widget.cluster.size,
                height: widget.cluster.size,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Outer energy ring
                    _buildEnergyRing(),
                    
                    // Main bubble body
                    _buildBubbleBody(),
                    
                    // Inner content preview
                    _buildContentPreview(),
                    
                    // Emotion indicator
                    _buildEmotionIndicator(),
                    
                    // Memory count badge
                    _buildMemoryCountBadge(),
                    
                    // Interaction ripples
                    if (_isHovered || widget.isActive) _buildInteractionRipples(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEnergyRing() {
    return Container(
      width: widget.cluster.size + 20,
      height: widget.cluster.size + 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            _getEmotionColor().withOpacity(0.0),
            _getEmotionColor().withOpacity(0.3 * _energyPulse.value),
            _getEmotionColor().withOpacity(0.0),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: _getEmotionColor().withOpacity(0.5 * _energyPulse.value),
            blurRadius: 30 * widget.cluster.energy,
            spreadRadius: 5 * widget.cluster.energy,
          ),
        ],
      ),
    );
  }

  Widget _buildBubbleBody() {
    return Container(
      width: widget.cluster.size,
      height: widget.cluster.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            widget.cluster.dominantColors.first.withOpacity(0.8),
            widget.cluster.dominantColors.last.withOpacity(0.6),
            NexusTheme.voidBlack.withOpacity(0.9),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
        boxShadow: NexusTheme.dimensionalShadow,
      ),
      child: ClipOval(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: _getEmotionColor().withOpacity(0.5),
                width: 2,
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.1),
                  Colors.transparent,
                  Colors.black.withOpacity(0.1),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentPreview() {
    return Container(
      width: widget.cluster.size * 0.6,
      height: widget.cluster.size * 0.6,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: SweepGradient(
          colors: widget.cluster.dominantColors,
          stops: List.generate(
            widget.cluster.dominantColors.length,
            (index) => index / (widget.cluster.dominantColors.length - 1),
          ),
        ),
      ),
      child: ClipOval(
        child: Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              colors: [
                Colors.transparent,
                NexusTheme.voidBlack.withOpacity(0.3),
              ],
            ),
          ),
          child: Center(
            child: Icon(
              _getEmotionIcon(),
              color: NexusTheme.crystalWhite.withOpacity(0.8),
              size: widget.cluster.size * 0.2,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmotionIndicator() {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        width: 16,
        height: 16,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: _getEmotionColor(),
          boxShadow: [
            BoxShadow(
              color: _getEmotionColor().withOpacity(0.6),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMemoryCountBadge() {
    return Positioned(
      bottom: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: NexusTheme.voidBlack.withOpacity(0.7),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getEmotionColor().withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Text(
          '${widget.cluster.itemCount}',
          style: NexusTheme.whisperText.copyWith(
            color: _getEmotionColor(),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildInteractionRipples() {
    return Container(
      width: widget.cluster.size + 40,
      height: widget.cluster.size + 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: _getEmotionColor().withOpacity(0.3 * widget.pulseIntensity),
          width: 2,
        ),
      ),
    );
  }

  IconData _getEmotionIcon() {
    switch (widget.cluster.emotion) {
      case EmotionType.joy:
        return Icons.sentiment_very_satisfied;
      case EmotionType.love:
        return Icons.favorite;
      case EmotionType.mystery:
        return Icons.psychology;
      case EmotionType.energy:
        return Icons.electric_bolt;
      case EmotionType.peace:
        return Icons.self_improvement;
      case EmotionType.nostalgia:
        return Icons.history;
      case EmotionType.adventure:
        return Icons.explore;
    }
  }
}

/// Floating Memory Preview - Shows when hovering over bubble
class MemoryPreview extends StatelessWidget {
  final MemoryCluster cluster;
  
  const MemoryPreview({super.key, required this.cluster});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200,
      height: 120,
      decoration: BoxDecoration(
        gradient: NexusTheme.memoryBubble,
        borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
        boxShadow: NexusTheme.dimensionalShadow,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cluster.title,
                  style: NexusTheme.memoryLabel.copyWith(
                    color: NexusTheme.crystalWhite,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${cluster.itemCount} memories',
                  style: NexusTheme.whisperText,
                ),
                const Spacer(),
                Row(
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      color: NexusTheme.goldenAura,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'AI Clustered',
                      style: NexusTheme.whisperText.copyWith(
                        color: NexusTheme.goldenAura,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
