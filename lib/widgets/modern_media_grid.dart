import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shimmer/shimmer.dart';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:photo_manager/photo_manager.dart';

import '../models/media_item.dart';
import '../providers/theme_provider.dart';
import '../screens/photo_editor_screen.dart';

class ModernMediaGrid extends StatefulWidget {
  final List<MediaItem> media;
  final Set<String> selectedItems;
  final bool isSelectionMode;
  final Function(MediaItem) onItemTap;
  final Function(MediaItem) onItemLongPress;
  final Function(MediaItem) onItemSelected;
  final VoidCallback? onLoadMore;
  final bool hasMoreMedia;
  final bool isLoadingMore;

  const ModernMediaGrid({
    super.key,
    required this.media,
    required this.selectedItems,
    required this.isSelectionMode,
    required this.onItemTap,
    required this.onItemLongPress,
    required this.onItemSelected,
    this.onLoadMore,
    this.hasMoreMedia = false,
    this.isLoadingMore = false,
  });

  @override
  State<ModernMediaGrid> createState() => _ModernMediaGridState();
}

class _ModernMediaGridState extends State<ModernMediaGrid>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();

    if (widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      widget.onLoadMore?.call();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index == widget.media.length) {
                    return _buildLoadingCard();
                  }

                  final item = widget.media[index];
                  return ModernMediaCard(
                    mediaItem: item,
                    isSelected: widget.selectedItems.contains(item.id),
                    isSelectionMode: widget.isSelectionMode,
                    index: index,
                    onTap: () => widget.onItemTap(item),
                    onLongPress: () => widget.onItemLongPress(item),
                    onSelectionChanged: () => widget.onItemSelected(item),
                    onEdit: () => _openPhotoEditor(item),
                  );
                },
                childCount: widget.media.length + (widget.hasMoreMedia ? 1 : 0),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: Colors.grey.shade100,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }

  void _openPhotoEditor(MediaItem item) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            PhotoEditorScreen(mediaItem: item),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }
}

class ModernMediaCard extends StatefulWidget {
  final MediaItem mediaItem;
  final bool isSelected;
  final bool isSelectionMode;
  final int index;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final VoidCallback onSelectionChanged;
  final VoidCallback onEdit;

  const ModernMediaCard({
    super.key,
    required this.mediaItem,
    required this.isSelected,
    required this.isSelectionMode,
    required this.index,
    required this.onTap,
    required this.onLongPress,
    required this.onSelectionChanged,
    required this.onEdit,
  });

  @override
  State<ModernMediaCard> createState() => _ModernMediaCardState();
}

class _ModernMediaCardState extends State<ModernMediaCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _selectionController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _selectionAnimation;
  Uint8List? _thumbnailData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _selectionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _selectionController,
      curve: Curves.elasticOut,
    ));

    _loadThumbnail();

    // Staggered animation
    Future.delayed(Duration(milliseconds: widget.index * 100), () {
      if (mounted) {
        _scaleController.forward();
      }
    });
  }

  Future<void> _loadThumbnail() async {
    try {
      if (widget.mediaItem.assetEntity != null) {
        final thumbnail = await widget.mediaItem.assetEntity!.thumbnailDataWithSize(
          const ThumbnailSize(300, 300),
          quality: 80,
        );
        if (mounted) {
          setState(() {
            _thumbnailData = thumbnail;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void didUpdateWidget(ModernMediaCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _selectionController.forward();
      } else {
        _selectionController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _selectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              widget.onTap();
            },
            onLongPress: () {
              HapticFeedback.mediumImpact();
              widget.onLongPress();
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: isDark 
                      ? Colors.black.withOpacity(0.3)
                      : Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    _buildThumbnail(isDark),
                    _buildGradientOverlay(),
                    _buildMediaInfo(),
                    _buildActionButtons(),
                    if (widget.isSelectionMode) _buildSelectionOverlay(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildThumbnail(bool isDark) {
    if (_isLoading) {
      return Shimmer.fromColors(
        baseColor: isDark ? Colors.grey.shade800 : Colors.grey.shade300,
        highlightColor: isDark ? Colors.grey.shade700 : Colors.grey.shade100,
        child: Container(
          color: Colors.white,
        ),
      );
    }

    if (_thumbnailData != null) {
      return Image.memory(
        _thumbnailData!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(isDark),
      );
    }

    return _buildErrorWidget(isDark);
  }

  Widget _buildErrorWidget(bool isDark) {
    return Container(
      color: isDark ? Colors.grey.shade800 : Colors.grey.shade200,
      child: Icon(
        Icons.broken_image_rounded,
        size: 48,
        color: isDark ? Colors.grey.shade600 : Colors.grey.shade400,
      ),
    );
  }

  Widget _buildGradientOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.7),
            ],
            stops: const [0.5, 1.0],
          ),
        ),
      ),
    );
  }

  Widget _buildMediaInfo() {
    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.mediaItem.type == MediaType.video) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.play_arrow_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDuration(Duration(seconds: widget.mediaItem.duration)),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
          ],
          Text(
            _formatDate(widget.mediaItem.createTime),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Positioned(
      top: 12,
      right: 12,
      child: Column(
        children: [
          _buildActionButton(
            icon: Icons.edit_rounded,
            onTap: widget.onEdit,
          ),
          const SizedBox(height: 8),
          _buildActionButton(
            icon: Icons.favorite_border_rounded,
            onTap: () {
              // TODO: Add to favorites
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(18),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }

  Widget _buildSelectionOverlay() {
    return AnimatedBuilder(
      animation: _selectionAnimation,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.lightPrimary.withOpacity(0.3),
          border: Border.all(
            color: AppColors.lightPrimary,
            width: 3,
          ),
        ),
        child: const Center(
          child: Icon(
            Icons.check_circle_rounded,
            color: Colors.white,
            size: 48,
          ),
        ),
      ),
      builder: (context, child) {
        return Opacity(
          opacity: widget.isSelected ? _selectionAnimation.value : 0.0,
          child: child,
        );
      },
    );
  }

  String _formatDuration(Duration? duration) {
    if (duration == null) return '0:00';
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
