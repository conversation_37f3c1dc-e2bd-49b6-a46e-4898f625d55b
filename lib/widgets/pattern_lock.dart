import 'package:flutter/material.dart';

import '../utils/constants.dart';

class PatternLock extends StatefulWidget {
  final Function(List<int>) onPatternEntered;

  const PatternLock({
    super.key,
    required this.onPatternEntered,
  });

  @override
  State<PatternLock> createState() => _PatternLockState();
}

class _PatternLockState extends State<PatternLock> {
  List<int> _pattern = [];
  List<int> _selectedDots = [];
  static const int _gridSize = 3;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Pattern grid
        Container(
          width: 240,
          height: 240,
          child: GestureDetector(
            onPanStart: _onPanStart,
            onPanUpdate: _onPanUpdate,
            onPanEnd: _onPanEnd,
            child: CustomPaint(
              painter: PatternPainter(
                selectedDots: _selectedDots,
                pattern: _pattern,
              ),
              child: GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: _gridSize,
                ),
                itemCount: _gridSize * _gridSize,
                itemBuilder: (context, index) {
                  return _buildDot(index);
                },
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Instructions
        Text(
          'Draw your pattern',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ],
    );
  }

  Widget _buildDot(int index) {
    final isSelected = _selectedDots.contains(index);
    
    return Center(
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isSelected
              ? AppConstants.primaryColor
              : Colors.grey[300],
          border: Border.all(
            color: isSelected
                ? AppConstants.primaryColor
                : Colors.grey[400]!,
            width: 2,
          ),
        ),
        child: isSelected
            ? const Icon(
                Icons.circle,
                color: Colors.white,
                size: 20,
              )
            : null,
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    final dotIndex = _getDotIndex(localPosition);
    
    if (dotIndex != -1) {
      setState(() {
        _selectedDots = [dotIndex];
        _pattern = [dotIndex];
      });
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_selectedDots.isEmpty) return;
    
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    final dotIndex = _getDotIndex(localPosition);
    
    if (dotIndex != -1 && !_selectedDots.contains(dotIndex)) {
      setState(() {
        _selectedDots.add(dotIndex);
        _pattern.add(dotIndex);
      });
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (_pattern.isNotEmpty) {
      widget.onPatternEntered(_pattern);
      setState(() {
        _selectedDots.clear();
        _pattern.clear();
      });
    }
  }

  int _getDotIndex(Offset position) {
    final dotSize = 40.0;
    final gridSize = 240.0;
    final cellSize = gridSize / _gridSize;
    
    final row = (position.dy / cellSize).floor();
    final col = (position.dx / cellSize).floor();
    
    if (row >= 0 && row < _gridSize && col >= 0 && col < _gridSize) {
      return row * _gridSize + col;
    }
    
    return -1;
  }
}

class PatternPainter extends CustomPainter {
  final List<int> selectedDots;
  final List<int> pattern;

  PatternPainter({
    required this.selectedDots,
    required this.pattern,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (selectedDots.length < 2) return;

    final paint = Paint()
      ..color = AppConstants.primaryColor
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    final cellSize = size.width / 3;

    for (int i = 0; i < selectedDots.length - 1; i++) {
      final currentDot = selectedDots[i];
      final nextDot = selectedDots[i + 1];

      final currentRow = currentDot ~/ 3;
      final currentCol = currentDot % 3;
      final nextRow = nextDot ~/ 3;
      final nextCol = nextDot % 3;

      final startPoint = Offset(
        currentCol * cellSize + cellSize / 2,
        currentRow * cellSize + cellSize / 2,
      );
      final endPoint = Offset(
        nextCol * cellSize + cellSize / 2,
        nextRow * cellSize + cellSize / 2,
      );

      canvas.drawLine(startPoint, endPoint, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
} 