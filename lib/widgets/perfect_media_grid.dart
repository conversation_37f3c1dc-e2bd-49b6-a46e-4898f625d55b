import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shimmer/shimmer.dart';
import 'dart:typed_data';
import 'package:photo_manager/photo_manager.dart';

import '../models/media_item.dart';
import '../providers/theme_provider.dart';
import '../screens/photo_editor_screen.dart';
import '../screens/viewer_screen.dart';

class PerfectMediaGrid extends StatefulWidget {
  final List<MediaItem> media;
  final Set<String> selectedItems;
  final bool isSelectionMode;
  final Function(MediaItem) onItemTap;
  final Function(MediaItem) onItemLongPress;
  final Function(MediaItem) onItemSelected;
  final VoidCallback? onLoadMore;
  final bool hasMoreMedia;
  final bool isLoadingMore;

  const PerfectMediaGrid({
    super.key,
    required this.media,
    required this.selectedItems,
    required this.isSelectionMode,
    required this.onItemTap,
    required this.onItemLongPress,
    required this.onItemSelected,
    this.onLoadMore,
    this.hasMoreMedia = false,
    this.isLoadingMore = false,
  });

  @override
  State<PerfectMediaGrid> createState() => _PerfectMediaGridState();
}

class _PerfectMediaGridState extends State<PerfectMediaGrid>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();

    if (widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      widget.onLoadMore?.call();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(8),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 1.0,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index == widget.media.length) {
                    return _buildLoadingCard();
                  }

                  final item = widget.media[index];
                  return PerfectMediaCard(
                    mediaItem: item,
                    isSelected: widget.selectedItems.contains(item.id),
                    isSelectionMode: widget.isSelectionMode,
                    index: index,
                    onTap: () => widget.onItemTap(item),
                    onLongPress: () => widget.onItemLongPress(item),
                    onSelectionChanged: () => widget.onItemSelected(item),
                    onEdit: () => _openPhotoEditor(item),
                  );
                },
                childCount: widget.media.length + (widget.hasMoreMedia ? 1 : 0),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade100,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }

  void _openPhotoEditor(MediaItem item) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            PhotoEditorScreen(mediaItem: item),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400),
      ),
    );
  }
}

class PerfectMediaCard extends StatefulWidget {
  final MediaItem mediaItem;
  final bool isSelected;
  final bool isSelectionMode;
  final int index;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final VoidCallback onSelectionChanged;
  final VoidCallback onEdit;

  const PerfectMediaCard({
    super.key,
    required this.mediaItem,
    required this.isSelected,
    required this.isSelectionMode,
    required this.index,
    required this.onTap,
    required this.onLongPress,
    required this.onSelectionChanged,
    required this.onEdit,
  });

  @override
  State<PerfectMediaCard> createState() => _PerfectMediaCardState();
}

class _PerfectMediaCardState extends State<PerfectMediaCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _selectionController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _selectionAnimation;
  Uint8List? _thumbnailData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _selectionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _selectionController,
      curve: Curves.easeInOut,
    ));

    _loadThumbnail();
  }

  Future<void> _loadThumbnail() async {
    try {
      if (widget.mediaItem.assetEntity != null) {
        final thumbnail = await widget.mediaItem.assetEntity!.thumbnailDataWithSize(
          const ThumbnailSize(200, 200),
          quality: 85,
        );
        if (mounted) {
          setState(() {
            _thumbnailData = thumbnail;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void didUpdateWidget(PerfectMediaCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _selectionController.forward();
      } else {
        _selectionController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _selectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        if (widget.isSelectionMode) {
          widget.onSelectionChanged();
        } else {
          widget.onTap();
        }
      },
      onLongPress: () {
        HapticFeedback.mediumImpact();
        widget.onLongPress();
      },
      onTapDown: (_) => _scaleController.forward(),
      onTapUp: (_) => _scaleController.reverse(),
      onTapCancel: () => _scaleController.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    _buildThumbnail(isDark),
                    if (widget.mediaItem.type == MediaType.video) _buildVideoIndicator(),
                    if (widget.isSelectionMode) _buildSelectionOverlay(),
                    if (!widget.isSelectionMode) _buildActionButton(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildThumbnail(bool isDark) {
    if (_isLoading) {
      return Shimmer.fromColors(
        baseColor: isDark ? Colors.grey.shade800 : Colors.grey.shade300,
        highlightColor: isDark ? Colors.grey.shade700 : Colors.grey.shade100,
        child: Container(
          color: Colors.white,
        ),
      );
    }

    if (_thumbnailData != null) {
      return Image.memory(
        _thumbnailData!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(isDark),
      );
    }

    return _buildErrorWidget(isDark);
  }

  Widget _buildErrorWidget(bool isDark) {
    return Container(
      color: isDark ? Colors.grey.shade800 : Colors.grey.shade200,
      child: Icon(
        Icons.broken_image_rounded,
        size: 32,
        color: isDark ? Colors.grey.shade600 : Colors.grey.shade400,
      ),
    );
  }

  Widget _buildVideoIndicator() {
    return Positioned(
      bottom: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.play_arrow_rounded,
              color: Colors.white,
              size: 12,
            ),
            const SizedBox(width: 2),
            Text(
              _formatDuration(Duration(seconds: widget.mediaItem.duration)),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionOverlay() {
    return AnimatedBuilder(
      animation: _selectionAnimation,
      child: Container(
        color: AppColors.lightPrimary.withOpacity(0.3),
        child: const Center(
          child: Icon(
            Icons.check_circle_rounded,
            color: Colors.white,
            size: 32,
          ),
        ),
      ),
      builder: (context, child) {
        return Opacity(
          opacity: widget.isSelected ? _selectionAnimation.value : 0.0,
          child: child,
        );
      },
    );
  }

  Widget _buildActionButton() {
    return Positioned(
      top: 8,
      right: 8,
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          widget.onEdit();
        },
        child: Container(
          width: 28,
          height: 28,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: const Icon(
            Icons.edit_rounded,
            color: Colors.white,
            size: 14,
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
