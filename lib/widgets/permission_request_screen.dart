import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';

import '../services/gallery_permission_service.dart';
import '../utils/constants.dart';

class PermissionRequestScreen extends StatefulWidget {
  const PermissionRequestScreen({super.key});

  @override
  State<PermissionRequestScreen> createState() => _PermissionRequestScreenState();
}

class _PermissionRequestScreenState extends State<PermissionRequestScreen>
    with TickerProviderStateMixin {
  final GalleryPermissionService _permissionService = GalleryPermissionService();
  
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  bool _isRequesting = false;
  int _currentStep = 0;
  
  final List<PermissionInfo> _permissions = [
    PermissionInfo(
      permission: Permission.photos,
      title: 'Photos & Videos',
      description: 'Access your photo library to display and organize your media',
      rationale: 'We need this to show your beautiful photos and videos in the gallery',
      isRequired: true,
      isGranted: false,
    ),
    PermissionInfo(
      permission: Permission.camera,
      title: 'Camera',
      description: 'Take new photos and videos directly from the app',
      rationale: 'Capture new memories and add them to your gallery instantly',
      isRequired: true,
      isGranted: false,
    ),
    PermissionInfo(
      permission: Permission.notification,
      title: 'Notifications',
      description: 'Get notified about backup completion and app updates',
      rationale: 'Stay informed about important gallery activities',
      isRequired: false,
      isGranted: false,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkCurrentPermissions();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeController.forward();
    _slideController.forward();
  }

  Future<void> _checkCurrentPermissions() async {
    for (int i = 0; i < _permissions.length; i++) {
      final status = await _permissions[i].permission.status;
      setState(() {
        _permissions[i] = PermissionInfo(
          permission: _permissions[i].permission,
          title: _permissions[i].title,
          description: _permissions[i].description,
          rationale: _permissions[i].rationale,
          isRequired: _permissions[i].isRequired,
          isGranted: status.isGranted,
        );
      });
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDark ? Colors.black : Colors.white,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildHeader(theme, isDark),
                        const SizedBox(height: 48),
                        _buildPermissionsList(theme, isDark),
                        const SizedBox(height: 32),
                        _buildProgressIndicator(theme, isDark),
                      ],
                    ),
                  ),
                  _buildActionButtons(theme, isDark),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark 
                ? [Colors.purple.shade400, Colors.blue.shade400]
                : [Colors.purple.shade600, Colors.blue.shade600],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: (isDark ? Colors.purple.shade400 : Colors.purple.shade600)
                    .withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            Icons.photo_library_rounded,
            size: 60,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Welcome to Simple Gallery',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          'To provide you with the best gallery experience, we need a few permissions',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: isDark ? Colors.grey.shade300 : Colors.grey.shade600,
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPermissionsList(ThemeData theme, bool isDark) {
    return Column(
      children: _permissions.asMap().entries.map((entry) {
        final index = entry.key;
        final permission = entry.value;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildPermissionCard(permission, theme, isDark, index),
        );
      }).toList(),
    );
  }

  Widget _buildPermissionCard(PermissionInfo permission, ThemeData theme, bool isDark, int index) {
    final isActive = index == _currentStep;
    final isCompleted = permission.isGranted;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isCompleted 
          ? (isDark ? Colors.green.shade900.withOpacity(0.3) : Colors.green.shade50)
          : isActive
            ? (isDark ? Colors.blue.shade900.withOpacity(0.3) : Colors.blue.shade50)
            : (isDark ? Colors.grey.shade900.withOpacity(0.3) : Colors.grey.shade50),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isCompleted 
            ? Colors.green.shade400
            : isActive
              ? Colors.blue.shade400
              : Colors.transparent,
          width: 2,
        ),
        boxShadow: isActive ? [
          BoxShadow(
            color: Colors.blue.shade400.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isCompleted 
                ? Colors.green.shade400
                : isActive
                  ? Colors.blue.shade400
                  : Colors.grey.shade400,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              isCompleted 
                ? Icons.check_rounded
                : _getPermissionIcon(permission.permission),
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      permission.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    if (permission.isRequired) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red.shade400,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Required',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  permission.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark ? Colors.grey.shade300 : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(ThemeData theme, bool isDark) {
    final completedCount = _permissions.where((p) => p.isGranted).length;
    final progress = completedCount / _permissions.length;
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: theme.textTheme.titleSmall?.copyWith(
                color: isDark ? Colors.grey.shade300 : Colors.grey.shade600,
              ),
            ),
            Text(
              '$completedCount/${_permissions.length}',
              style: theme.textTheme.titleSmall?.copyWith(
                color: isDark ? Colors.grey.shade300 : Colors.grey.shade600,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: isDark ? Colors.grey.shade800 : Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(
            progress == 1.0 ? Colors.green.shade400 : Colors.blue.shade400,
          ),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme, bool isDark) {
    final allRequired = _permissions.where((p) => p.isRequired).every((p) => p.isGranted);
    
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: _isRequesting ? null : (allRequired ? _continueToApp : _requestPermissions),
            style: ElevatedButton.styleFrom(
              backgroundColor: allRequired ? Colors.green.shade400 : Colors.blue.shade400,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: _isRequesting
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  allRequired ? 'Continue to Gallery' : 'Grant Permissions',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
          ),
        ),
        const SizedBox(height: 12),
        TextButton(
          onPressed: _isRequesting ? null : () => Navigator.of(context).pop(),
          child: Text(
            'Skip for now',
            style: TextStyle(
              color: isDark ? Colors.grey.shade400 : Colors.grey.shade600,
            ),
          ),
        ),
      ],
    );
  }

  IconData _getPermissionIcon(Permission permission) {
    switch (permission) {
      case Permission.photos:
        return Icons.photo_library_rounded;
      case Permission.camera:
        return Icons.camera_alt_rounded;
      case Permission.notification:
        return Icons.notifications_rounded;
      default:
        return Icons.security_rounded;
    }
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      HapticFeedback.lightImpact();
      
      for (int i = 0; i < _permissions.length; i++) {
        if (!_permissions[i].isGranted) {
          setState(() {
            _currentStep = i;
          });
          
          await Future.delayed(const Duration(milliseconds: 300));
          
          final result = await _permissionService.requestPermissionWithRationale(_permissions[i].permission);
          
          setState(() {
            _permissions[i] = PermissionInfo(
              permission: _permissions[i].permission,
              title: _permissions[i].title,
              description: _permissions[i].description,
              rationale: _permissions[i].rationale,
              isRequired: _permissions[i].isRequired,
              isGranted: result == PermissionRequestResult.granted,
            );
          });
          
          if (result == PermissionRequestResult.granted) {
            HapticFeedback.selectionClick();
          }
        }
      }
    } finally {
      setState(() {
        _isRequesting = false;
      });
    }
  }

  void _continueToApp() {
    HapticFeedback.lightImpact();
    Navigator.of(context).pushReplacementNamed('/home');
  }
}
