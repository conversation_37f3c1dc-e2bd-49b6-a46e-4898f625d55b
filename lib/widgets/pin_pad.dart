import 'package:flutter/material.dart';

import '../utils/constants.dart';

class PinPad extends StatefulWidget {
  final Function(String) onPinEntered;

  const PinPad({
    super.key,
    required this.onPinEntered,
  });

  @override
  State<PinPad> createState() => _PinPadState();
}

class _PinPadState extends State<PinPad> {
  String _pin = '';
  static const int _pinLength = 4;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // PIN dots
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(_pinLength, (index) {
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index < _pin.length
                    ? AppConstants.primaryColor
                    : Colors.grey[300],
              ),
            );
          }),
        ),
        
        const SizedBox(height: 32),
        
        // Number pad
        Column(
          children: [
            for (int row = 0; row < 3; row++)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  for (int col = 0; col < 3; col++)
                    _buildNumberButton(row * 3 + col + 1),
                ],
              ),
            
            // Bottom row (0, delete, empty)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(width: 80), // Empty space
                _buildNumberButton(0),
                _buildDeleteButton(),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNumberButton(int number) {
    return Container(
      margin: const EdgeInsets.all(8),
      child: GestureDetector(
        onTap: () {
          if (_pin.length < _pinLength) {
            setState(() {
              _pin += number.toString();
            });
            
            if (_pin.length == _pinLength) {
              widget.onPinEntered(_pin);
              setState(() {
                _pin = '';
              });
            }
          }
        },
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              number.toString(),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeleteButton() {
    return Container(
      margin: const EdgeInsets.all(8),
      child: GestureDetector(
        onTap: () {
          if (_pin.isNotEmpty) {
            setState(() {
              _pin = _pin.substring(0, _pin.length - 1);
            });
          }
        },
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            shape: BoxShape.circle,
          ),
          child: const Center(
            child: Icon(
              Icons.backspace,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }
} 