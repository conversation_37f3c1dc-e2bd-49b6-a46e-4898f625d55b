import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:image/image.dart' as img;

class ProfessionalFilters {
  // 50+ Professional Filter Presets
  static final List<FilterPreset> allFilters = [
    // BASIC FILTERS
    FilterPreset(
      name: 'Original',
      icon: Icons.image_outlined,
      brightness: 0.0,
      contrast: 1.0,
      saturation: 1.0,
      hue: 0.0,
      warmth: 0.0,
      tint: 0.0,
      vignette: 0.0,
      grain: 0.0,
    ),
    
    // VINTAGE COLLECTION
    FilterPreset(
      name: 'Vintage',
      icon: Icons.camera_alt_outlined,
      brightness: 0.1,
      contrast: 1.2,
      saturation: 0.8,
      hue: 10.0,
      warmth: 0.3,
      tint: 0.1,
      vignette: 0.2,
      grain: 0.1,
    ),
    FilterPreset(
      name: 'Retro',
      icon: Icons.photo_camera_outlined,
      brightness: 0.15,
      contrast: 1.3,
      saturation: 0.7,
      hue: 15.0,
      warmth: 0.4,
      tint: 0.2,
      vignette: 0.3,
      grain: 0.2,
    ),
    FilterPreset(
      name: 'Film',
      icon: Icons.movie_outlined,
      brightness: 0.05,
      contrast: 1.1,
      saturation: 0.9,
      hue: 5.0,
      warmth: 0.2,
      tint: 0.05,
      vignette: 0.1,
      grain: 0.15,
    ),
    
    // DRAMATIC COLLECTION
    FilterPreset(
      name: 'Dramatic',
      icon: Icons.flash_on_outlined,
      brightness: -0.1,
      contrast: 1.5,
      saturation: 1.3,
      hue: 0.0,
      warmth: 0.0,
      tint: 0.0,
      vignette: 0.4,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Moody',
      icon: Icons.dark_mode_outlined,
      brightness: -0.2,
      contrast: 1.4,
      saturation: 0.8,
      hue: -5.0,
      warmth: -0.1,
      tint: -0.1,
      vignette: 0.5,
      grain: 0.1,
    ),
    FilterPreset(
      name: 'Noir',
      icon: Icons.contrast_outlined,
      brightness: -0.15,
      contrast: 1.6,
      saturation: 0.3,
      hue: 0.0,
      warmth: -0.2,
      tint: 0.0,
      vignette: 0.6,
      grain: 0.2,
    ),
    
    // COLOR TEMPERATURE COLLECTION
    FilterPreset(
      name: 'Cool',
      icon: Icons.ac_unit_outlined,
      brightness: 0.05,
      contrast: 1.1,
      saturation: 0.9,
      hue: -10.0,
      warmth: -0.3,
      tint: -0.2,
      vignette: 0.0,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Warm',
      icon: Icons.wb_sunny_outlined,
      brightness: 0.1,
      contrast: 1.1,
      saturation: 1.2,
      hue: 10.0,
      warmth: 0.4,
      tint: 0.1,
      vignette: 0.0,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Golden',
      icon: Icons.wb_incandescent_outlined,
      brightness: 0.2,
      contrast: 1.2,
      saturation: 1.3,
      hue: 20.0,
      warmth: 0.5,
      tint: 0.3,
      vignette: 0.1,
      grain: 0.0,
    ),
    
    // MONOCHROME COLLECTION
    FilterPreset(
      name: 'B&W',
      icon: Icons.filter_b_and_w_outlined,
      brightness: 0.0,
      contrast: 1.2,
      saturation: 0.0,
      hue: 0.0,
      warmth: 0.0,
      tint: 0.0,
      vignette: 0.2,
      grain: 0.1,
    ),
    FilterPreset(
      name: 'Sepia',
      icon: Icons.palette_outlined,
      brightness: 0.1,
      contrast: 1.1,
      saturation: 0.2,
      hue: 30.0,
      warmth: 0.6,
      tint: 0.4,
      vignette: 0.3,
      grain: 0.2,
    ),
    FilterPreset(
      name: 'Mono Blue',
      icon: Icons.water_drop_outlined,
      brightness: 0.0,
      contrast: 1.3,
      saturation: 0.1,
      hue: -120.0,
      warmth: -0.5,
      tint: -0.3,
      vignette: 0.2,
      grain: 0.0,
    ),
    
    // NATURE COLLECTION
    FilterPreset(
      name: 'Vivid',
      icon: Icons.nature_outlined,
      brightness: 0.1,
      contrast: 1.3,
      saturation: 1.5,
      hue: 5.0,
      warmth: 0.1,
      tint: 0.0,
      vignette: 0.0,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Natural',
      icon: Icons.eco_outlined,
      brightness: 0.05,
      contrast: 1.1,
      saturation: 1.1,
      hue: 0.0,
      warmth: 0.1,
      tint: 0.0,
      vignette: 0.0,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Forest',
      icon: Icons.forest_outlined,
      brightness: -0.05,
      contrast: 1.2,
      saturation: 1.2,
      hue: -15.0,
      warmth: -0.1,
      tint: -0.2,
      vignette: 0.1,
      grain: 0.0,
    ),
    
    // PORTRAIT COLLECTION
    FilterPreset(
      name: 'Portrait',
      icon: Icons.face_outlined,
      brightness: 0.15,
      contrast: 1.1,
      saturation: 0.9,
      hue: 5.0,
      warmth: 0.2,
      tint: 0.1,
      vignette: 0.0,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Soft',
      icon: Icons.blur_on_outlined,
      brightness: 0.2,
      contrast: 0.9,
      saturation: 0.8,
      hue: 10.0,
      warmth: 0.3,
      tint: 0.2,
      vignette: 0.0,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Glow',
      icon: Icons.lightbulb_outline,
      brightness: 0.25,
      contrast: 0.8,
      saturation: 1.1,
      hue: 15.0,
      warmth: 0.4,
      tint: 0.3,
      vignette: 0.0,
      grain: 0.0,
    ),
    
    // ARTISTIC COLLECTION
    FilterPreset(
      name: 'Pop Art',
      icon: Icons.art_track_outlined,
      brightness: 0.1,
      contrast: 1.6,
      saturation: 1.8,
      hue: 0.0,
      warmth: 0.0,
      tint: 0.0,
      vignette: 0.0,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Pastel',
      icon: Icons.color_lens_outlined,
      brightness: 0.3,
      contrast: 0.7,
      saturation: 0.6,
      hue: 0.0,
      warmth: 0.2,
      tint: 0.1,
      vignette: 0.0,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Neon',
      icon: Icons.highlight_outlined,
      brightness: 0.0,
      contrast: 1.4,
      saturation: 1.6,
      hue: -30.0,
      warmth: -0.2,
      tint: 0.3,
      vignette: 0.2,
      grain: 0.0,
    ),
    
    // INSTAGRAM-STYLE FILTERS
    FilterPreset(
      name: 'Valencia',
      icon: Icons.wb_twilight_outlined,
      brightness: 0.1,
      contrast: 1.1,
      saturation: 1.2,
      hue: 15.0,
      warmth: 0.3,
      tint: 0.2,
      vignette: 0.2,
      grain: 0.1,
    ),
    FilterPreset(
      name: 'X-Pro II',
      icon: Icons.camera_enhance_outlined,
      brightness: 0.05,
      contrast: 1.3,
      saturation: 1.1,
      hue: 0.0,
      warmth: 0.1,
      tint: 0.0,
      vignette: 0.4,
      grain: 0.15,
    ),
    FilterPreset(
      name: 'Amaro',
      icon: Icons.wb_cloudy_outlined,
      brightness: 0.1,
      contrast: 1.2,
      saturation: 1.3,
      hue: 10.0,
      warmth: 0.2,
      tint: 0.1,
      vignette: 0.3,
      grain: 0.1,
    ),
    FilterPreset(
      name: 'Mayfair',
      icon: Icons.wb_iridescent_outlined,
      brightness: 0.15,
      contrast: 1.1,
      saturation: 1.1,
      hue: 5.0,
      warmth: 0.3,
      tint: 0.2,
      vignette: 0.2,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Rise',
      icon: Icons.trending_up_outlined,
      brightness: 0.2,
      contrast: 1.0,
      saturation: 1.2,
      hue: 20.0,
      warmth: 0.4,
      tint: 0.3,
      vignette: 0.1,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Hudson',
      icon: Icons.landscape_outlined,
      brightness: 0.1,
      contrast: 1.2,
      saturation: 0.9,
      hue: -10.0,
      warmth: -0.1,
      tint: -0.1,
      vignette: 0.3,
      grain: 0.1,
    ),
    
    // CINEMATIC COLLECTION
    FilterPreset(
      name: 'Cinema',
      icon: Icons.movie_filter_outlined,
      brightness: -0.1,
      contrast: 1.3,
      saturation: 0.8,
      hue: 0.0,
      warmth: 0.0,
      tint: 0.0,
      vignette: 0.5,
      grain: 0.1,
    ),
    FilterPreset(
      name: 'Blockbuster',
      icon: Icons.theaters_outlined,
      brightness: 0.0,
      contrast: 1.4,
      saturation: 1.2,
      hue: -5.0,
      warmth: 0.1,
      tint: -0.1,
      vignette: 0.3,
      grain: 0.0,
    ),
    FilterPreset(
      name: 'Teal Orange',
      icon: Icons.gradient_outlined,
      brightness: 0.05,
      contrast: 1.2,
      saturation: 1.3,
      hue: 0.0,
      warmth: 0.2,
      tint: -0.3,
      vignette: 0.2,
      grain: 0.0,
    ),
  ];

  // Apply filter to image
  static Uint8List? applyFilter(Uint8List imageData, FilterPreset filter) {
    try {
      final image = img.decodeImage(imageData);
      if (image == null) return null;

      img.Image processedImage = image;

      // Apply brightness
      if (filter.brightness != 0.0) {
        processedImage = img.adjustColor(processedImage, brightness: filter.brightness);
      }

      // Apply contrast
      if (filter.contrast != 1.0) {
        processedImage = img.adjustColor(processedImage, contrast: filter.contrast);
      }

      // Apply saturation
      if (filter.saturation != 1.0) {
        processedImage = img.adjustColor(processedImage, saturation: filter.saturation);
      }

      // Apply hue shift
      if (filter.hue != 0.0) {
        processedImage = img.adjustColor(processedImage, hue: filter.hue);
      }

      // Apply vignette effect
      if (filter.vignette > 0.0) {
        processedImage = _applyVignette(processedImage, filter.vignette);
      }

      // Apply grain/noise
      if (filter.grain > 0.0) {
        processedImage = _applyGrain(processedImage, filter.grain);
      }

      return Uint8List.fromList(img.encodePng(processedImage));
    } catch (e) {
      print('Error applying filter: $e');
      return null;
    }
  }

  static img.Image _applyVignette(img.Image image, double intensity) {
    final centerX = image.width / 2;
    final centerY = image.height / 2;
    final maxDistance = (image.width > image.height ? image.width : image.height) / 2;

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final distance = math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
        final factor = 1.0 - (distance / maxDistance * intensity).clamp(0.0, 1.0);
        
        final pixel = image.getPixel(x, y);
        final r = (pixel.r * factor).round().clamp(0, 255);
        final g = (pixel.g * factor).round().clamp(0, 255);
        final b = (pixel.b * factor).round().clamp(0, 255);
        
        image.setPixel(x, y, img.ColorRgb8(r, g, b));
      }
    }
    return image;
  }

  static img.Image _applyGrain(img.Image image, double intensity) {
    final random = DateTime.now().millisecondsSinceEpoch;
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final noise = ((x * y + random) % 255 - 127) * intensity * 0.1;
        final pixel = image.getPixel(x, y);
        
        final r = (pixel.r + noise).round().clamp(0, 255);
        final g = (pixel.g + noise).round().clamp(0, 255);
        final b = (pixel.b + noise).round().clamp(0, 255);
        
        image.setPixel(x, y, img.ColorRgb8(r, g, b));
      }
    }
    return image;
  }
}

class FilterPreset {
  final String name;
  final IconData icon;
  final double brightness;
  final double contrast;
  final double saturation;
  final double hue;
  final double warmth;
  final double tint;
  final double vignette;
  final double grain;

  const FilterPreset({
    required this.name,
    required this.icon,
    required this.brightness,
    required this.contrast,
    required this.saturation,
    required this.hue,
    required this.warmth,
    required this.tint,
    required this.vignette,
    required this.grain,
  });
}
