import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui';
import '../design_system/nexus_theme.dart';
import '../screens/nexus_home_screen.dart';

/// Quantum Search - AI-powered natural language search interface
/// Features morphing search bar with neural network visualization
class QuantumSearch extends StatefulWidget {
  final bool isActive;
  final VoidCallback onToggle;
  final Function(String) onSearch;

  const QuantumSearch({
    super.key,
    required this.isActive,
    required this.onToggle,
    required this.onSearch,
  });

  @override
  State<QuantumSearch> createState() => _QuantumSearchState();
}

class _QuantumSearchState extends State<QuantumSearch>
    with TickerProviderStateMixin {
  late AnimationController _morphController;
  late AnimationController _neuralController;
  late AnimationController _pulseController;
  
  late Animation<double> _morphProgress;
  late Animation<double> _neuralActivity;
  late Animation<double> _pulseIntensity;
  
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  
  bool _isSearching = false;
  List<String> _suggestions = [];
  List<SearchNeuron> _neurons = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generateNeuralNetwork();
    _setupSearchSuggestions();
  }

  void _initializeAnimations() {
    _morphController = AnimationController(
      duration: NexusAnimations.cosmic,
      vsync: this,
    );
    _morphProgress = CurvedAnimation(
      parent: _morphController,
      curve: NexusTheme.warpCurve,
    );

    _neuralController = AnimationController(
      duration: NexusAnimations.universal,
      vsync: this,
    );
    _neuralActivity = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _neuralController, curve: Curves.linear),
    );
    _neuralController.repeat();

    _pulseController = AnimationController(
      duration: NexusAnimations.organic,
      vsync: this,
    );
    _pulseIntensity = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _pulseController.repeat(reverse: true);
  }

  void _generateNeuralNetwork() {
    final random = math.Random();
    _neurons.clear();
    
    for (int i = 0; i < 20; i++) {
      _neurons.add(SearchNeuron(
        position: Offset(
          random.nextDouble(),
          random.nextDouble(),
        ),
        connections: List.generate(
          random.nextInt(3) + 1,
          (index) => random.nextInt(20),
        ),
        activity: random.nextDouble(),
        type: NeuronType.values[random.nextInt(NeuronType.values.length)],
      ));
    }
  }

  void _setupSearchSuggestions() {
    _suggestions = [
      "Show me beach photos from last summer",
      "Find pictures with my family",
      "Photos taken at sunset",
      "Happy moments from this year",
      "Pictures with dogs",
      "Vacation memories",
      "Food photos from restaurants",
      "Selfies with friends",
      "Nature and landscape shots",
      "Birthday celebrations",
    ];
  }

  @override
  void didUpdateWidget(QuantumSearch oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _morphController.forward();
        _focusNode.requestFocus();
      } else {
        _morphController.reverse();
        _focusNode.unfocus();
      }
    }
  }

  @override
  void dispose() {
    _morphController.dispose();
    _neuralController.dispose();
    _pulseController.dispose();
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;
    
    setState(() {
      _isSearching = true;
    });
    
    // Simulate AI processing
    Future.delayed(const Duration(milliseconds: 1500), () {
      setState(() {
        _isSearching = false;
      });
      widget.onSearch(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _morphController,
        _neuralController,
        _pulseController,
      ]),
      builder: (context, child) {
        return Stack(
          children: [
            // Neural network background
            if (widget.isActive) _buildNeuralNetwork(),
            
            // Main search interface
            _buildSearchInterface(),
            
            // AI suggestions
            if (widget.isActive && !_isSearching) _buildSuggestions(),
            
            // Processing indicator
            if (_isSearching) _buildProcessingIndicator(),
          ],
        );
      },
    );
  }

  Widget _buildNeuralNetwork() {
    return Positioned.fill(
      child: CustomPaint(
        painter: NeuralNetworkPainter(
          neurons: _neurons,
          activity: _neuralActivity.value,
          intensity: _pulseIntensity.value,
        ),
      ),
    );
  }

  Widget _buildSearchInterface() {
    final searchWidth = 60.0 + (MediaQuery.of(context).size.width - 120) * _morphProgress.value;
    
    return Container(
      height: 60,
      width: searchWidth,
      decoration: BoxDecoration(
        gradient: widget.isActive 
            ? NexusTheme.cosmicStorm 
            : RadialGradient(
                colors: [
                  NexusTheme.electricBlue.withOpacity(0.3),
                  NexusTheme.cosmicPurple.withOpacity(0.1),
                ],
              ),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: NexusTheme.electricBlue.withOpacity(0.3 * _pulseIntensity.value),
            blurRadius: 20,
            spreadRadius: 5,
          ),
          ...NexusTheme.dimensionalShadow,
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: NexusTheme.electricBlue.withOpacity(0.5),
                width: 2,
              ),
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.1),
                  Colors.transparent,
                ],
              ),
            ),
            child: Row(
              children: [
                // Search icon/button
                GestureDetector(
                  onTap: widget.onToggle,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          NexusTheme.electricBlue.withOpacity(0.8),
                          NexusTheme.cosmicPurple.withOpacity(0.6),
                        ],
                      ),
                    ),
                    child: Icon(
                      widget.isActive ? Icons.close : NexusIcons.cosmicSearch,
                      color: NexusTheme.crystalWhite,
                      size: 24,
                    ),
                  ),
                ),
                
                // Search input field
                if (widget.isActive)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: TextField(
                        controller: _searchController,
                        focusNode: _focusNode,
                        style: NexusTheme.quantumText.copyWith(
                          color: NexusTheme.crystalWhite,
                        ),
                        decoration: InputDecoration(
                          hintText: "Ask me anything about your memories...",
                          hintStyle: NexusTheme.whisperText,
                          border: InputBorder.none,
                          suffixIcon: _isSearching
                              ? SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      NexusTheme.neonGreen,
                                    ),
                                  ),
                                )
                              : IconButton(
                                  icon: Icon(
                                    Icons.send,
                                    color: NexusTheme.neonGreen,
                                  ),
                                  onPressed: () => _performSearch(_searchController.text),
                                ),
                        ),
                        onSubmitted: _performSearch,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestions() {
    return Positioned(
      top: 80,
      left: 0,
      right: 0,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          gradient: NexusTheme.memoryBubble,
          borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
          boxShadow: NexusTheme.dimensionalShadow,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        color: NexusTheme.goldenAura,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        "AI Suggestions",
                        style: NexusTheme.memoryLabel.copyWith(
                          color: NexusTheme.crystalWhite,
                        ),
                      ),
                    ],
                  ),
                ),
                ...(_suggestions.take(5).map((suggestion) => _buildSuggestionItem(suggestion))),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestionItem(String suggestion) {
    return GestureDetector(
      onTap: () {
        _searchController.text = suggestion;
        _performSearch(suggestion);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(
              color: NexusTheme.electricBlue.withOpacity(0.2),
              width: 1,
            ),
          ),
        ),
        child: Text(
          suggestion,
          style: NexusTheme.quantumText.copyWith(
            color: NexusTheme.crystalWhite.withOpacity(0.8),
          ),
        ),
      ),
    );
  }

  Widget _buildProcessingIndicator() {
    return Positioned(
      top: 80,
      left: 0,
      right: 0,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        height: 100,
        decoration: BoxDecoration(
          gradient: NexusTheme.memoryBubble,
          borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
          boxShadow: NexusTheme.dimensionalShadow,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(NexusTheme.bubbleRadius),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        NexusTheme.neonGreen,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    "AI is analyzing your memories...",
                    style: NexusTheme.quantumText.copyWith(
                      color: NexusTheme.crystalWhite,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Neural Network Painter
class NeuralNetworkPainter extends CustomPainter {
  final List<SearchNeuron> neurons;
  final double activity;
  final double intensity;

  NeuralNetworkPainter({
    required this.neurons,
    required this.activity,
    required this.intensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Paint neural connections
    final connectionPaint = Paint()
      ..color = NexusTheme.electricBlue.withOpacity(0.3 * intensity)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    for (final neuron in neurons) {
      final neuronPos = Offset(
        neuron.position.dx * size.width,
        neuron.position.dy * size.height,
      );

      for (final connectionIndex in neuron.connections) {
        if (connectionIndex < neurons.length) {
          final targetPos = Offset(
            neurons[connectionIndex].position.dx * size.width,
            neurons[connectionIndex].position.dy * size.height,
          );
          canvas.drawLine(neuronPos, targetPos, connectionPaint);
        }
      }
    }

    // Paint neurons
    for (final neuron in neurons) {
      final position = Offset(
        neuron.position.dx * size.width,
        neuron.position.dy * size.height,
      );

      final neuronPaint = Paint()
        ..color = _getNeuronColor(neuron.type).withOpacity(
          neuron.activity * intensity * (0.5 + 0.5 * math.sin(activity)),
        );

      canvas.drawCircle(position, 4, neuronPaint);
    }
  }

  Color _getNeuronColor(NeuronType type) {
    switch (type) {
      case NeuronType.input:
        return NexusTheme.neonGreen;
      case NeuronType.processing:
        return NexusTheme.electricBlue;
      case NeuronType.memory:
        return NexusTheme.magentaFlare;
      case NeuronType.output:
        return NexusTheme.goldenAura;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Data models
class SearchNeuron {
  final Offset position;
  final List<int> connections;
  final double activity;
  final NeuronType type;

  const SearchNeuron({
    required this.position,
    required this.connections,
    required this.activity,
    required this.type,
  });
}

enum NeuronType { input, processing, memory, output }
