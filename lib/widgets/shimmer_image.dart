import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'dart:math' as math;

class ShimmerImage extends StatefulWidget {
  final Uint8List? imageData;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? errorWidget;

  const ShimmerImage({
    super.key,
    required this.imageData,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.errorWidget,
  });

  @override
  State<ShimmerImage> createState() => _ShimmerImageState();
}

class _ShimmerImageState extends State<ShimmerImage>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;
  late AnimationController _fadeController;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _fadeAnimation;
  
  bool _imageLoaded = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _startShimmer();
    _checkImageData();
  }

  void _startShimmer() {
    _shimmerController.repeat();
  }

  void _stopShimmer() {
    _shimmerController.stop();
    _fadeController.forward();
  }

  void _checkImageData() {
    if (widget.imageData != null) {
      // Simulate loading delay for shimmer effect
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            _imageLoaded = true;
          });
          _stopShimmer();
        }
      });
    } else {
      setState(() {
        _hasError = true;
      });
      _stopShimmer();
    }
  }

  @override
  void didUpdateWidget(ShimmerImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.imageData != oldWidget.imageData) {
      setState(() {
        _imageLoaded = false;
        _hasError = false;
      });
      _fadeController.reset();
      _startShimmer();
      _checkImageData();
    }
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius,
      ),
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Background shimmer
            if (!_imageLoaded && !_hasError) _buildShimmer(isDark),
            
            // Actual image
            if (_imageLoaded && widget.imageData != null)
              FadeTransition(
                opacity: _fadeAnimation,
                child: Image.memory(
                  widget.imageData!,
                  width: widget.width,
                  height: widget.height,
                  fit: widget.fit,
                  errorBuilder: (context, error, stackTrace) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        setState(() {
                          _hasError = true;
                        });
                      }
                    });
                    return _buildErrorWidget(isDark);
                  },
                ),
              ),
            
            // Error widget
            if (_hasError) _buildErrorWidget(isDark),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmer(bool isDark) {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDark ? [
                Colors.grey.shade800,
                Colors.grey.shade700,
                Colors.grey.shade600,
                Colors.grey.shade700,
                Colors.grey.shade800,
              ] : [
                Colors.grey.shade300,
                Colors.grey.shade200,
                Colors.grey.shade100,
                Colors.grey.shade200,
                Colors.grey.shade300,
              ],
              stops: [
                math.max(0.0, _shimmerAnimation.value - 0.3),
                math.max(0.0, _shimmerAnimation.value - 0.15),
                math.max(0.0, _shimmerAnimation.value),
                math.min(1.0, _shimmerAnimation.value + 0.15),
                math.min(1.0, _shimmerAnimation.value + 0.3),
              ],
            ),
          ),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white.withOpacity(0.0),
                  Colors.white.withOpacity(0.1),
                  Colors.white.withOpacity(0.2),
                  Colors.white.withOpacity(0.1),
                  Colors.white.withOpacity(0.0),
                ],
                stops: [
                  math.max(0.0, _shimmerAnimation.value - 0.2),
                  math.max(0.0, _shimmerAnimation.value - 0.1),
                  math.max(0.0, _shimmerAnimation.value),
                  math.min(1.0, _shimmerAnimation.value + 0.1),
                  math.min(1.0, _shimmerAnimation.value + 0.2),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget(bool isDark) {
    return widget.errorWidget ?? Container(
      color: isDark ? Colors.grey.shade800 : Colors.grey.shade200,
      child: Center(
        child: Icon(
          Icons.broken_image_rounded,
          size: math.min(widget.width ?? 48, widget.height ?? 48) * 0.4,
          color: isDark ? Colors.grey.shade600 : Colors.grey.shade400,
        ),
      ),
    );
  }
}

class ShimmerImageGrid extends StatelessWidget {
  final int itemCount;
  final double aspectRatio;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const ShimmerImageGrid({
    super.key,
    required this.itemCount,
    this.aspectRatio = 1.0,
    this.crossAxisCount = 3,
    this.crossAxisSpacing = 4,
    this.mainAxisSpacing = 4,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: aspectRatio,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
      ),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return ShimmerImage(
          imageData: null,
          borderRadius: BorderRadius.circular(8),
        );
      },
    );
  }
}
